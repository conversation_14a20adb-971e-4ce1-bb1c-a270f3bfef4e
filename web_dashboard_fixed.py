#!/usr/bin/env python3
"""
Web Dashboard Module for Trend-Crawler - Fixed Version

This module provides a web interface with dashboards and reports for the
trend-crawler system, including:
- Scraper performance metrics
- CAPTCHA detection statistics
- Proxy performance monitoring
- Alerts and notifications
- System health overview
- User authentication and management
"""

import os
import datetime
import logging
import platform
from typing import Dict, Any, List, Optional
from pathlib import Path
import secrets
from datetime import timedelta

# Web framework
from fastapi import (
    FastAPI, APIRouter, HTTPException, Depends, Request, Form, status
)
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import CryptContext
from passlib.exc import UnknownHashError
from pydantic import BaseModel, Field

# Import monitoring system
try:
    from monitoring import monitor
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    logging.warning(
        "Monitoring module not available. Some features will be limited."
    )

# Import CAPTCHA solver
try:
    from captcha_solver import captcha_solver
    CAPTCHA_SOLVER_AVAILABLE = True
except ImportError:
    CAPTCHA_SOLVER_AVAILABLE = False
    logging.warning(
        "CAPTCHA solver not available. Some features will be limited."
    )

# Try importing PostgreSQL
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor, DictCursor
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
    logging.warning("PostgreSQL support not available. Some features will be limited.")

# Try importing psutil for system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError as e:
    PSUTIL_AVAILABLE = False
    logging.error(
        "Failed to import 'psutil'. System monitoring features will be disabled. "
        "This usually means the package is not installed in your Python environment. "
        "Please ensure 'psutil' is listed in your 'requirements.txt' and install dependencies via "
        "'pip install -r requirements.txt' in your active 'trend-crawler' environment. "
        f"Original error: {e}"
    )

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_fallback_detection_stats():
    """Return fallback detection stats when CAPTCHA solver is not available."""
    return {
        "recaptcha_detected": 0,
        "hcaptcha_detected": 0,
        "cloudflare_detected": 0,
        "other_detected": 0,
        "total_detected": 0
    }


# Current year for templates (avoids using Django-specific {% now %} tag)
def get_current_year():
    """Return the current year as a string."""
    return str(datetime.datetime.now().year)

# JWT Settings
SECRET_KEY = os.environ.get("SECRET_KEY", secrets.token_hex(32))
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 8  # 8 hours

# Security settings
DEFAULT_ADMIN_EMAIL = os.environ.get("ADMIN_EMAIL", "<EMAIL>")
DEFAULT_ADMIN_FULLNAME = os.environ.get("ADMIN_FULLNAME", "Admin User")
DEFAULT_ADMIN_USERNAME = os.environ.get("ADMIN_USERNAME", "admin")
ENFORCE_PASSWORD_CHANGE = True  # Enforce password change on first login

# Store whether any password changes have been applied since startup
# This helps us track if first-login password change has occurred
ADMIN_FIRST_LOGIN = True

def generate_secure_password():
    """Generate a secure random password or use the one from environment.

    This ensures that when deployed in production, we can set a secure password
    via environment variables instead of hardcoding it.
    """
    # Check if password is provided via environment variable
    env_password = os.environ.get("ADMIN_PASSWORD")
    if (env_password):
        return env_password

    # Use 'admin' as the default password
    password = "admin"

    # Log that we're using the default password
    logger.warning("Using default admin password 'admin'. User will be forced to change on first login.")
    return password

# Password hasher
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token")
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token", auto_error=False)

# User models
class User(BaseModel):
    """Base user model."""
    username: str
    email: str
    full_name: Optional[str] = None
    disabled: Optional[bool] = False
    is_admin: Optional[bool] = False

class UserInDB(User):
    """User model with password hash."""
    password_hash: str

class Token(BaseModel):
    """Token model for JWT authentication."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Token data model."""
    username: str

class UserCreate(BaseModel):
    """User creation model."""
    username: str
    email: str
    password: str
    full_name: Optional[str] = None
    is_admin: Optional[bool] = False

class PasswordChange(BaseModel):
    """Password change model."""
    current_password: str
    new_password: str

# Proxy model for adding new proxies
class ProxyCreate(BaseModel):
    """Request model for creating a new proxy."""
    ip: str
    port: int
    type: str  # "http", "https", "socks4", "socks5"
    group: str  # "us", "eu", "asia", "residential", "datacenter"
    username: Optional[str] = None
    password: Optional[str] = None
    location: Optional[str] = None

# CAPTCHA Feedback model
class CaptchaFeedbackRequest(BaseModel):
    """Request model for CAPTCHA feedback."""
    captcha_id: str
    correct_solution: Optional[str] = None
    feedback_type: str
    comments: Optional[str] = None

# Integration Test model
class IntegrationTestRequest(BaseModel):
    """Request model for integration tests."""
    target_type: str  # "scraper", "proxy", "captcha_solver"
    target_name: str
    test_type: str  # "connectivity", "performance", "accuracy"
    params: Optional[Dict[str, Any]] = None

# Fallback in-memory database for when PostgreSQL is not available
USERS_DB = {}

# User database connection
def get_db_connection():
    """Get a database connection."""
    if not POSTGRES_AVAILABLE:
        raise Exception("PostgreSQL support not available")

    try:
        conn = psycopg2.connect(
            host=os.environ.get("DB_HOST", "localhost"),
            port=os.environ.get("DB_PORT", "5432"),
            dbname=os.environ.get("DB_NAME", "scraper_metrics"),
            user=os.environ.get("DB_USER", "postgres"),
            password=os.environ.get("DB_PASSWORD", "postgres")
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise

# Rest of the existing functions (init_user_db, verify_password, etc.)
# ... [keeping all the existing auth functions]

# Create FastAPI app
app = FastAPI(
    title="Trend Crawler Dashboard",
    description="Web dashboard for the trend-crawler system",
    version="1.0.0"
)

# Create static directory if it doesn't exist
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
css_dir = static_dir / "css"
css_dir.mkdir(exist_ok=True)
js_dir = static_dir / "js"
js_dir.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
templates_dir = Path("templates")
templates_dir.mkdir(exist_ok=True)

# Create templates
templates = Jinja2Templates(directory="templates")

# Add get_current_year function to template context
templates.env.globals["get_current_year"] = get_current_year

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create router for API endpoints
api_router = APIRouter(prefix="/api/v1")

# FIXED SYSTEM INFO ENDPOINT
@api_router.get("/system/info")
async def get_system_info():
    """Get system information including CPU, memory, disk usage, etc."""
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            try:
                sys_info_data = monitor.get_system_info()
                if sys_info_data:
                    return sys_info_data
            except Exception as e:
                logger.warning(f"Error getting system info from monitoring: {e}")

        # If monitoring is not available or returned no data, try to collect it directly
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil not available, returning fallback system info")
            return {
                "hostname": platform.node() if hasattr(platform, 'node') else "Unknown",
                "cpu_usage": "N/A (psutil not available)",
                "memory_usage": "N/A (psutil not available)",
                "disk_usage": "N/A (psutil not available)",
                "os_info": f"{platform.system()} {platform.release()}",
                "kernel": platform.platform(),
                "cpu_info": platform.processor() if hasattr(platform, 'processor') else "Unknown",
                "python_version": platform.python_version(),
                "uptime": "N/A (psutil not available)",
                "load_avg": "N/A (psutil not available)"
            }

        # Get CPU info
        cpu_usage = psutil.cpu_percent(interval=1)

        # Get memory info
        mem_info = psutil.virtual_memory()
        mem_used = round(mem_info.used / (1024 * 1024))
        mem_total = round(mem_info.total / (1024 * 1024))
        mem_percent = mem_info.percent

        # Get disk info
        disk_info = psutil.disk_usage('/')
        disk_used = round(disk_info.used / (1024 * 1024 * 1024))
        disk_total = round(disk_info.total / (1024 * 1024 * 1024))
        disk_percent = disk_info.percent

        # Get system uptime
        boot_timestamp = datetime.datetime.fromtimestamp(psutil.boot_time())
        current_uptime = datetime.datetime.now() - boot_timestamp
        uptime_str = f"up {current_uptime.days} days {current_uptime.seconds // 3600}:{(current_uptime.seconds // 60) % 60:02d}"

        # Get load average (Unix-like systems only)
        try:
            load_average = psutil.getloadavg()
            load_avg_str = f"{load_average[0]:.2f}, {load_average[1]:.2f}, {load_average[2]:.2f}"
        except (AttributeError, OSError):
            load_avg_str = "N/A (not supported on this OS)"

        # Format system info
        system_info = {
            "hostname": platform.node(),
            "cpu_usage": f"{cpu_usage:.1f}%",
            "memory_usage": f"{mem_used}/{mem_total}MB ({mem_percent:.1f}%)",
            "disk_usage": f"{disk_used}/{disk_total}GB ({disk_percent:.1f}%)",
            "os_info": f"{platform.system()} {platform.release()} {platform.version()}",
            "kernel": platform.platform(),
            "cpu_info": platform.processor(),
            "python_version": platform.python_version(),
            "uptime": uptime_str,
            "load_avg": load_avg_str
        }

        return system_info
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        # Return fallback data with fields to avoid frontend errors
        return {
            "hostname": "N/A",
            "cpu_usage": "N/A",
            "memory_usage": "N/A",
            "disk_usage": "N/A",
            "os_info": "N/A",
            "kernel": "N/A",
            "cpu_info": "N/A",
            "python_version": "N/A",
            "uptime": "N/A",
            "load_avg": "N/A"
        }

@api_router.get("/system/resources")
async def get_system_resources(hours: int = 1):
    """Get historical system resource usage (CPU, memory, disk)."""
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            try:
                resources_data = monitor.get_system_resources(hours)
                if resources_data:
                    return resources_data
            except Exception as e:
                logger.warning(f"Error getting system resources from monitoring: {e}")

        # If monitoring is not available, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

                cursor.execute("""
                    SELECT
                        timestamp,
                        cpu_usage,
                        memory_usage,
                        disk_usage
                    FROM system_metrics
                    WHERE timestamp > %s
                    ORDER BY timestamp ASC
                """, (time_limit,))

                result_set = cursor.fetchall()
                cursor.close()
                conn.close()

                # Format the timestamps to ISO format
                for row in result_set:
                    if row['timestamp']:
                        row['timestamp'] = row['timestamp'].isoformat()

                return {
                    "labels": [r['timestamp'] for r in result_set],
                    "cpu_usage": [r['cpu_usage'] for r in result_set],
                    "memory_usage": [r['memory_usage'] for r in result_set],
                    "disk_usage": [r['disk_usage'] for r in result_set]
                }
            except Exception as db_error:
                logger.error(f"Error getting system resources from database: {db_error}")

        # Return empty data as a fallback
        logger.warning("Neither monitoring nor database available for system resources")
        return {
            "labels": [],
            "cpu_usage": [],
            "memory_usage": [],
            "disk_usage": []
        }
    except Exception as e:
        logger.error(f"Error getting system resources: {e}")
        return {
            "labels": [],
            "cpu_usage": [],
            "memory_usage": [],
            "disk_usage": []
        }

@api_router.get("/system/api-health")
async def get_api_health(hours: int = 24):
    """Get API service health stats."""
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            try:
                health_data = monitor.get_api_health()
                if health_data:
                    return health_data
            except Exception as e:
                logger.warning(f"Error getting API health from monitoring: {e}")

        # Return fallback empty data
        logger.warning("Monitoring not available for API health")
        return {
            "api_names": [],
            "success_rates": [],
            "response_times": []
        }
    except Exception as e:
        logger.error(f"Error getting API health: {e}")
        return {
            "api_names": [],
            "success_rates": [],
            "response_times": []
        }

@api_router.get("/system/db-performance")
async def get_db_performance(hours: int = 1):
    """Get database performance metrics."""
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            try:
                perf_data = monitor.get_db_performance(hours)
                if perf_data:
                    return perf_data
            except Exception as e:
                logger.warning(f"Error getting DB performance from monitoring: {e}")

        # Return fallback empty data
        logger.warning("Monitoring not available for DB performance")
        return {
            "timestamps": [],
            "query_times": [],
            "connections": []
        }
    except Exception as e:
        logger.error(f"Error getting DB performance: {e}")
        return {
            "timestamps": [],
            "query_times": [],
            "connections": []
        }

@api_router.get("/system/events")
async def get_system_events(limit: int = 50):
    """Get recent system events."""
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            try:
                events_data = monitor.get_system_events(limit)
                if events_data:
                    # Format events for display
                    current_time = datetime.datetime.now()
                    formatted_events = []
                    
                    for event in events_data:
                        # Handle different event formats
                        if isinstance(event, dict) and 'timestamp' in event:
                            timestamp_str = event['timestamp']
                            try:
                                if isinstance(timestamp_str, str):
                                    ts_val = datetime.datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                else:
                                    ts_val = timestamp_str
                                
                                # Format timestamp for display
                                if ts_val.date() == current_time.date():
                                    display_time_str = f"Today {ts_val.strftime('%H:%M')}"
                                elif ts_val.date() == (current_time - datetime.timedelta(days=1)).date():
                                    display_time_str = f"Yesterday {ts_val.strftime('%H:%M')}"
                                else:
                                    display_time_str = ts_val.strftime('%Y-%m-%d %H:%M')
                            except:
                                display_time_str = str(timestamp_str)
                        else:
                            display_time_str = "Unknown"
                        
                        formatted_events.append({
                            "time": display_time_str,
                            "type": event.get('event_type', 'Unknown'),
                            "message": event.get('message', str(event))
                        })
                    
                    return formatted_events
            except Exception as e:
                logger.warning(f"Error getting system events from monitoring: {e}")

        # Return empty data as fallback
        logger.warning("Monitoring not available for system events")
        return []
    except Exception as e:
        logger.error(f"Error getting system events: {e}")
        return []

# Include the API router
app.include_router(api_router)

# Add a simple test endpoint
@app.get("/test")
async def test_endpoint():
    """Test endpoint to verify the server is running."""
    return {"status": "ok", "message": "Server is running"}

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting web dashboard. Run with 'python web_dashboard.py'")
    uvicorn.run(app, host="0.0.0.0", port=8080)
