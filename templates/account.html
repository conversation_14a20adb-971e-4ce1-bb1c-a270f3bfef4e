{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>Account Settings</h2>

    {% if prompt_change_message %}
    <div class="alert alert-warning" role="alert" style="margin-bottom: 1rem;">
        {{ prompt_change_message }}
    </div>
    {% endif %}

    <form id="change-password-form">
        <h3>Change Password</h3>
        <div class="form-group">
            <label for="current_password">Current Password</label>
            <input type="password" id="current_password" name="current_password" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="new_password">New Password</label>
            <input type="password" id="new_password" name="new_password" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="confirm_new_password">Confirm New Password</label>
            <input type="password" id="confirm_new_password" name="confirm_new_password" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">Change Password</button>
        <div id="change-password-message" style="margin-top: 1rem;"></div>
    </form>
    
    <hr style="margin: 2rem 0;">

    <h3>User Profile</h3>
    <p><strong>Username:</strong> {{ user.username }}</p>
    <p><strong>Email:</strong> {{ user.email }}</p>
    <p><strong>Full Name:</strong> {{ user.full_name if user.full_name else 'Not set' }}</p>
    
    <!-- Placeholder for other account settings -->

</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const changePasswordForm = document.getElementById('change-password-form');
    const changePasswordMessage = document.getElementById('change-password-message'); // Ensure this div exists in the HTML part

    if (changePasswordForm && changePasswordMessage) {
        changePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmNewPassword = document.getElementById('confirm_new_password').value;

            changePasswordMessage.innerHTML = ''; // Clear previous messages

            if (newPassword !== confirmNewPassword) {
                changePasswordMessage.innerHTML = '<div class="alert alert-error">New passwords do not match.</div>';
                return;
            }
            if (!newPassword) {
                changePasswordMessage.innerHTML = '<div class="alert alert-error">New password cannot be empty.</div>';
                return;
            }
            if (!currentPassword) { // Also check current password
                changePasswordMessage.innerHTML = '<div class="alert alert-error">Current password cannot be empty.</div>';
                return;
            }

            const payload = {
                current_password: currentPassword,
                new_password: newPassword
            };

            try {
                const response = await fetch('/api/v1/users/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
                    },
                    body: JSON.stringify(payload)
                });

                const responseData = await response.json();

                if (response.ok) {
                    changePasswordMessage.innerHTML = `<div class="alert alert-success">${responseData.message || 'Password updated successfully! Redirecting...'}</div>`;
                    if (responseData.access_token) {
                        localStorage.setItem('access_token', responseData.access_token);
                    }
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000); // Redirect after 2 seconds
                } else {
                    changePasswordMessage.innerHTML = `<div class="alert alert-error">${responseData.detail || 'Failed to change password.'}</div>`;
                }
            } catch (error) {
                console.error('Password change error:', error);
                changePasswordMessage.innerHTML = '<div class="alert alert-error">An unexpected error occurred. Please try again.</div>';
            }
        });
    }
});
</script>
{% endblock %}
