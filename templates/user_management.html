{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>User Management</h2>
    
    <div class="user-actions">
        <button id="add-user-btn" class="btn btn-primary">Add New User</button>
    </div>
    
    <div class="divider"></div>
    
    <div class="users-table-container" id="users-table">
        <p>Loading users...</p>
    </div>
</div>

<!-- Add User Modal -->
<div id="addUserModal" class="modal">
    <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2>Add New User</h2>
        <form id="add-user-form" class="form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="full_name">Full Name</label>
                <input type="text" id="full_name" name="full_name" class="form-control">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="is_admin">
                    <input type="checkbox" id="is_admin" name="is_admin">
                    Admin Privileges
                </label>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Add User</button>
            </div>
        </form>
        <div id="add-user-message" class="message-container"></div>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="modal">
    <div class="modal-content">
        <span class="close-modal">&times;</span>
        <h2>Reset User Password</h2>
        <form id="reset-password-form" class="form">
            <input type="hidden" id="reset-username" name="reset-username">
            <div class="form-group">
                <label for="reset-password">New Password</label>
                <input type="password" id="reset-password" name="reset-password" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Reset Password</button>
            </div>
        </form>
        <div id="reset-password-message" class="message-container"></div>
    </div>
</div>
{% endblock %}

{% block head %}
<style>
.user-actions {
    margin-bottom: 1rem;
}

.divider {
    border-top: 1px solid #ddd;
    margin: 1.5rem 0;
}

.user-row {
    display: flex;
    align-items: center;
}

.user-role {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 8px;
}

.user-role.admin {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.user-actions-cell {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 2px 8px;
    font-size: 0.8rem;
}

.btn-reset {
    background-color: #f39c12;
}

.btn-delete {
    background-color: #e74c3c;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 500px;
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover,
.close-modal:focus {
    color: #333;
    text-decoration: none;
}

.message-container {
    margin-top: 1rem;
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addUserBtn = document.getElementById('add-user-btn');
    const addUserModal = document.getElementById('addUserModal');
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    const closeModals = document.querySelectorAll('.close-modal');
    
    // Load users when page loads
    loadUsers();
    
    // Open Add User modal
    addUserBtn.addEventListener('click', function() {
        addUserModal.style.display = 'block';
    });
    
    // Close modals when clicking on X
    closeModals.forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            addUserModal.style.display = 'none';
            resetPasswordModal.style.display = 'none';
        });
    });
    
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === addUserModal) {
            addUserModal.style.display = 'none';
        }
        if (event.target === resetPasswordModal) {
            resetPasswordModal.style.display = 'none';
        }
    });
    
    // Add User form submission
    const addUserForm = document.getElementById('add-user-form');
    const addUserMessage = document.getElementById('add-user-message');
    
    addUserForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const userData = {
            username: document.getElementById('username').value,
            email: document.getElementById('email').value,
            password: document.getElementById('password').value,
            full_name: document.getElementById('full_name').value || undefined,
            is_admin: document.getElementById('is_admin').checked
        };
        
        fetch('/api/v1/users/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(userData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            addUserMessage.innerHTML = `<div class="alert alert-success">
                <p>User ${data.username} added successfully!</p>
            </div>`;
            addUserForm.reset();
            loadUsers(); // Reload the users list
            
            // Close modal after a short delay
            setTimeout(() => {
                addUserModal.style.display = 'none';
                addUserMessage.innerHTML = '';
            }, 2000);
        })
        .catch(error => {
            addUserMessage.innerHTML = `<div class="alert alert-error">
                <p>${error.message}</p>
            </div>`;
        });
    });
    
    // Reset Password form submission
    const resetPasswordForm = document.getElementById('reset-password-form');
    const resetPasswordMessage = document.getElementById('reset-password-message');
    
    resetPasswordForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('reset-username').value;
        const newPassword = document.getElementById('reset-password').value;
        
        // Create form data
        const formData = new FormData();
        formData.append('new_password', newPassword);
        
        fetch(`/api/v1/users/${username}/reset-password`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            resetPasswordMessage.innerHTML = `<div class="alert alert-success">
                <p>${data.message}</p>
            </div>`;
            resetPasswordForm.reset();
            
            // Close modal after a short delay
            setTimeout(() => {
                resetPasswordModal.style.display = 'none';
                resetPasswordMessage.innerHTML = '';
            }, 2000);
        })
        .catch(error => {
            resetPasswordMessage.innerHTML = `<div class="alert alert-error">
                <p>${error.message}</p>
            </div>`;
        });
    });
    
    // Load users function
    function loadUsers() {
        const usersTable = document.getElementById('users-table');
        
        fetch('/api/v1/users/', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(users => {
            if (users.length === 0) {
                usersTable.innerHTML = '<p>No users found.</p>';
                return;
            }
            
            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Full Name</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            users.forEach(user => {
                tableHTML += `
                    <tr>
                        <td>
                            <div class="user-row">
                                ${user.username}
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td>${user.full_name || '-'}</td>
                        <td>
                            <span class="user-role ${user.is_admin ? 'admin' : ''}">
                                ${user.is_admin ? 'Admin' : 'User'}
                            </span>
                        </td>
                        <td>${user.disabled ? 'Disabled' : 'Active'}</td>
                        <td class="user-actions-cell">
                            <button class="btn btn-small btn-reset reset-password-btn" data-username="${user.username}">
                                Reset Password
                            </button>
                            <button class="btn btn-small btn-delete delete-user-btn" data-username="${user.username}">
                                Delete
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableHTML += `
                    </tbody>
                </table>
            `;
            
            usersTable.innerHTML = tableHTML;
            
            // Add event listeners to the reset password buttons
            document.querySelectorAll('.reset-password-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    document.getElementById('reset-username').value = username;
                    resetPasswordModal.style.display = 'block';
                });
            });
            
            // Add event listeners to the delete user buttons
            document.querySelectorAll('.delete-user-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                        deleteUser(username);
                    }
                });
            });
        })
        .catch(error => {
            usersTable.innerHTML = `<div class="alert alert-error">
                <p>Error loading users: ${error.message}</p>
            </div>`;
        });
    }
    
    // Delete user function
    function deleteUser(username) {
        fetch(`/api/v1/users/${username}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            alert(data.message);
            loadUsers(); // Reload the users list
        })
        .catch(error => {
            alert(`Error deleting user: ${error.message}`);
        });
    }
});
</script>
{% endblock %}