{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card full-width">
        <h2>System Information</h2>
        <div id="systemInfo"><p>Loading system information...</p></div>
    </div>

    <div class="dashboard-card">
        <h2>System Resource Usage (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="systemResourcesChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>API Service Health (Last 24h)</h2>
        <div class="chart-container">
            <canvas id="apiHealthChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Database Performance (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="dbPerformanceChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card full-width">
        <h2>Recent System Events</h2>
        <div id="systemEvents" class="events-list"><p>Loading system events...</p></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const systemInfoDiv = document.getElementById('systemInfo');
    const systemEventsDiv = document.getElementById('systemEvents');

    // Ensure divs exist before trying to update them
    if (!systemInfoDiv) {
        console.error("System page error: 'systemInfo' div not found.");
        return; // Stop if essential elements are missing
    }
    if (!systemEventsDiv) {
        console.error("System page error: 'systemEvents' div not found.");
        return; // Stop if essential elements are missing
    }

    // Simplified fetchWithAuth for example (actual implementation might be more complex)
    // This assumes the cookie handles auth for GET requests, or token is in localStorage for manual header setting
    async function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('access_token'); // Still attempt to use localStorage for JS-initiated calls
        const headers = {
            'Content-Type': 'application/json', // Good practice to set content type for fetch
            ...options.headers
        };
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        // Cookies will be sent automatically by the browser for same-origin requests.
        return fetch(url, { ...options, headers });
    }

    function formatKey(key) {
        const result = key.replace(/_/g, " ").replace(/([A-Z])/g, " $1"); // Handle snake_case first, then camelCase
        return result.charAt(0).toUpperCase() + result.slice(1).trim();
    }

    function loadSystemInfo() {
        console.log("Attempting to load system info...");
        systemInfoDiv.innerHTML = '<p>Loading system information...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/info')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System info API response:", data);
                if (data && data.hostname && data.hostname !== 'N/A' && data.hostname !== null) {
                    let html = '<dl class="system-info-list">'; // Using a definition list for semantics
                    for (const key in data) {
                        if (data.hasOwnProperty(key)) {
                             html += `<dt>${formatKey(key)}</dt><dd>${data[key]}</dd>`;
                        }
                    }
                    html += '</dl>';
                    systemInfoDiv.innerHTML = html;
                } else {
                    console.log("System info unavailable (psutil likely missing). API returned:", data);
                    systemInfoDiv.innerHTML = '<p class="alert alert-warning">System information is currently unavailable. This usually means the \'psutil\' Python package is not installed or not functioning correctly in the server environment.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading system info:', error);
                systemInfoDiv.innerHTML = `<p class="alert alert-danger">Error loading system information: ${error.message}</p>`;
            });
    }

    function loadSystemEvents() {
        console.log("Attempting to load system events...");
        systemEventsDiv.innerHTML = '<p>Loading system events...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/events')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System events API response:", data);

                if (data && Array.isArray(data) && data.length > 0) {
                    let html = '';
                    data.forEach(event => {
                        html += `<div class="event-item alert alert-info">
                                     <span class="timestamp"><strong>Time:</strong> ${event.time || 'N/A'}</span>
                                     <strong class="event-type">Type:</strong> ${event.type || 'N/A'}
                                     <p><strong>Message:</strong> ${event.message || 'N/A'}</p>
                                 </div>`;
                    });
                    systemEventsDiv.innerHTML = html;
                } else {
                    systemEventsDiv.innerHTML = '<p class="alert alert-info">No recent system events found.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading system events:', error);
                systemEventsDiv.innerHTML = `<p class="alert alert-danger">Error loading system events: ${error.message}</p>`;
            });
    }

    // Load initial data
    loadSystemInfo();
    loadSystemEvents();

    // Initialize charts
    initSystemResourcesChart();
    initApiHealthChart();
    initDbPerformanceChart();

    function initSystemResourcesChart() {
        const canvas = document.getElementById('systemResourcesChart');
        if (!canvas) return;

        fetchWithAuth('/api/v1/system/resources?hours=1')
            .then(response => response.json())
            .then(data => {
                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels || [],
                        datasets: [
                            {
                                label: 'CPU Usage (%)',
                                data: data.cpu_usage || [],
                                borderColor: 'rgb(255, 99, 132)',
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                tension: 0.1
                            },
                            {
                                label: 'Memory Usage (%)',
                                data: data.memory_usage || [],
                                borderColor: 'rgb(54, 162, 235)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.1
                            },
                            {
                                label: 'Disk Usage (%)',
                                data: data.disk_usage || [],
                                borderColor: 'rgb(255, 205, 86)',
                                backgroundColor: 'rgba(255, 205, 86, 0.2)',
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'System Resource Usage'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading system resources chart:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">Could not load system resources chart.</p>';
            });
    }

    function initApiHealthChart() {
        const canvas = document.getElementById('apiHealthChart');
        if (!canvas) return;

        fetchWithAuth('/api/v1/system/api-health')
            .then(response => response.json())
            .then(data => {
                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.api_names || [],
                        datasets: [
                            {
                                label: 'Success Rate (%)',
                                data: data.success_rates || [],
                                backgroundColor: 'rgba(75, 192, 192, 0.6)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'API Service Health'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading API health chart:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">Could not load API health chart.</p>';
            });
    }

    function initDbPerformanceChart() {
        const canvas = document.getElementById('dbPerformanceChart');
        if (!canvas) return;

        fetchWithAuth('/api/v1/system/db-performance?hours=1')
            .then(response => response.json())
            .then(data => {
                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.timestamps || [],
                        datasets: [
                            {
                                label: 'Query Time (ms)',
                                data: data.query_times || [],
                                borderColor: 'rgb(153, 102, 255)',
                                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                                tension: 0.1,
                                yAxisID: 'y'
                            },
                            {
                                label: 'Active Connections',
                                data: data.active_connections || [],
                                borderColor: 'rgb(255, 159, 64)',
                                backgroundColor: 'rgba(255, 159, 64, 0.2)',
                                tension: 0.1,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Database Performance'
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                grid: {
                                    drawOnChartArea: false,
                                },
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading database performance chart:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">Could not load database performance chart.</p>';
            });
    }

});
</script>
{% endblock %}

{% block head %}
<style>
.full-width {
    grid-column: 1 / -1; /* Span all columns */
}
.system-info-list dt {
    font-weight: bold;
    margin-top: 0.5rem;
}
.system-info-list dd {
    margin-left: 1rem;
    margin-bottom: 0.5rem;
}
.events-list .event-item {
    margin-bottom: 10px;
    padding: 10px;
    border-left-width: 5px;
}
.events-list .timestamp {
    display: block;
    font-size: 0.9em;
    color: #555;
    margin-bottom: 5px;
}
.events-list .event-type {
    font-weight: bold;
}
</style>
{% endblock %}
