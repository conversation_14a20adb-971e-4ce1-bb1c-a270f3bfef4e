{% extends "base.html" %}

{% block content %}
<div class="filters">
    <div class="form-group">
        <label for="scraper-select">Select Scraper:</label>
        <select id="scraper-select" class="form-control">
            <option value="all">All Scrapers</option>
            <option value="twitter_x_scraper">Twitter/X Scraper</option>
            <option value="trend_crawler">Trend Crawler</option>
        </select>
    </div>
    <div class="form-group">
        <label for="time-range">Time Range:</label>
        <select id="time-range" class="form-control">
            <option value="24">Last 24 Hours</option>
            <option value="48">Last 48 Hours</option>
            <option value="168">Last 7 Days</option>
            <option value="720">Last 30 Days</option>
        </select>
    </div>
    <button id="apply-filters" class="btn">Apply Filters</button>
</div>

<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Success Rate</h2>
        <div class="chart-container">
            <canvas id="successRateChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Response Time</h2>
        <div class="chart-container">
            <canvas id="responseTimeChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>CAPTCHA Rates</h2>
        <div class="chart-container">
            <canvas id="captchaRateChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Request Volume</h2>
        <div class="chart-container">
            <canvas id="requestVolumeChart"></canvas>
        </div>
    </div>
</div>

<div class="dashboard-card">
    <h2>Scraper Performance Data</h2>
    <div class="table-container" id="scraperTable">
        <p>Loading scraper data...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const applyFiltersBtn = document.getElementById('apply-filters');
    applyFiltersBtn.addEventListener('click', loadScraperData);

    // Initial data load
    loadScraperData();

    function loadScraperData() {
        const scraperSelect = document.getElementById('scraper-select');
        const timeRange = document.getElementById('time-range');

        const scraper = scraperSelect.value;
        const hours = timeRange.value;

        const url = scraper === 'all'
            ? `/api/v1/stats/scrapers?scraper_name=all&hours=${hours}`
            : `/api/v1/stats/scrapers?scraper_name=${scraper}&hours=${hours}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                updateScraperCharts(data);
                updateScraperTable(data);
            })
            .catch(error => {
                handleApiError(error, 'scraperTable');
            });
    }

    function updateScraperCharts(data) {
        updateSuccessRateChart(data);
        updateResponseTimeChart(data);
        updateCaptchaRateChart(data);
        updateRequestVolumeChart(data);
    }

    function updateSuccessRateChart(data) {
        const ctx = document.getElementById('successRateChart').getContext('2d');

        // Clear existing chart if any
        if (window.successRateChart && typeof window.successRateChart.destroy === 'function') {
            window.successRateChart.destroy();
        }

        // Process data for chart
        let labels, successRates;

        if (Array.isArray(data)) {
            const validData = data.filter(item => item && typeof item === 'object');
            labels = validData.map(item => item.scraper_name);
            successRates = validData.map(item => {
                if (item.total_requests > 0) {
                    return (item.successful_requests / item.total_requests) * 100;
                }
                return 0;
            });
        } else { // Assuming data is an object
            const validKeys = Object.keys(data).filter(key => data[key] && typeof data[key] === 'object');
            labels = validKeys;
            successRates = validKeys.map(key => {
                const item = data[key];
                if (item.total_requests > 0) {
                    return (item.successful_requests / item.total_requests) * 100;
                }
                return 0;
            });
        }

        window.successRateChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Success Rate (%)',
                    data: successRates,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    function updateResponseTimeChart(data) {
        // Similar implementation to success rate chart
        // but focusing on response time metrics
        const ctx = document.getElementById('responseTimeChart').getContext('2d');

        // Generate dummy data for now
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                datasets: [{
                    label: 'Avg Response Time (s)',
                    data: [0.8, 1.2, 0.9, 1.1, 0.7, 1.3, 0.6],
                    borderColor: 'rgba(255, 159, 64, 1)',
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function updateCaptchaRateChart(data) {
        // Implementation for CAPTCHA rate chart
        const ctx = document.getElementById('captchaRateChart').getContext('2d');

        // Generate dummy data for now
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Twitter/X', 'Google', 'TradingView', 'CMC'],
                datasets: [{
                    label: 'CAPTCHA Rate (%)',
                    data: [15, 8, 12, 5],
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    function updateRequestVolumeChart(data) {
        // Implementation for request volume chart
        const ctx = document.getElementById('requestVolumeChart').getContext('2d');

        // Generate dummy data for now
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                datasets: [{
                    label: 'Requests',
                    data: [150, 230, 180, 250, 120, 300, 200],
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function updateScraperTable(data) {
        const tableContainer = document.getElementById('scraperTable');

        let tableHTML = `
        <table>
            <thead>
                <tr>
                    <th>Scraper</th>
                    <th>Total Requests</th>
                    <th>Success Rate</th>
                    <th>CAPTCHA Rate</th>
                    <th>Avg Response Time</th>
                </tr>
            </thead>
            <tbody>
        `;

        if (Array.isArray(data)) {
            data.forEach(item => {
                if (item) { // Add null check for item
                    tableHTML += createScraperRow(item);
                }
            });
        } else { // Assuming data is an object
            Object.keys(data).forEach(key => {
                const item = data[key];
                if (item) { // Add null check for item
                    tableHTML += createScraperRow({...item, scraper_name: key});
                }
            });
        }

        tableHTML += `
            </tbody>
        </table>
        `;

        tableContainer.innerHTML = tableHTML;
    }

    function createScraperRow(item) {
        if (!item) {
            console.error("Received null or undefined item in createScraperRow:", item);
            return ''; 
        }
        const successRate = (item.total_requests && item.total_requests > 0)
            ? ( (item.successful_requests || 0) / item.total_requests) * 100
            : 0;

        const captchaRate = (item.total_requests && item.total_requests > 0)
            ? ( (item.captcha_count || 0) / item.total_requests) * 100
            : 0;

        return `
        <tr>
            <td>${item.scraper_name || 'N/A'}</td>
            <td>${item.total_requests || 0}</td>
            <td>${successRate.toFixed(1)}%</td>
            <td>${captchaRate.toFixed(1)}%</td>
            <td>${item.avg_response_time ? item.avg_response_time.toFixed(2) + 's' : 'N/A'}</td>
        </tr>
        `;
    }
});
</script>
{% endblock %}