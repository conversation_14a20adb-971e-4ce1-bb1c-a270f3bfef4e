{% extends "base.html" %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2>Login</h2>
        <form id="login-form" class="auth-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Login</button>
            </div>
            <div class="auth-message" id="login-message"></div>
        </form>
        <div class="auth-footer">
            <p>Don't have an account? <a href="/register">Register</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginMessage = document.getElementById('login-message');

    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // Create form data for API submission
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        fetch('/api/v1/auth/token', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Login failed');
            }
            return response.json();
        })
        .then(data => {
            // Store the token in localStorage
            localStorage.setItem('access_token', data.access_token);

            // Check for needs_password_change flag and redirect accordingly
            if (data.needs_password_change === true) {
                window.location.href = '/account'; // Redirect to account page to change password
            } else {
                window.location.href = '/dashboard'; // Redirect to dashboard
            }
        })
        .catch(error => {
            loginMessage.innerHTML = `<div class="alert alert-error">
                <p>Invalid username or password.</p>
            </div>`;
        });
    });
});
</script>
{% endblock %}