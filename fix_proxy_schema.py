#!/usr/bin/env python3
"""
Fix for proxy_metrics schema issue - adds the missing columns needed for dashboard
"""

import os
import logging
import psycopg2
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_proxy_metrics_table():
    """Add missing columns to proxy_metrics table to fix dashboard errors."""
    try:
        # Connect to database
        conn = psycopg2.connect(
            host="localhost", 
            port=5432,
            database="scraper_metrics",
            user="postgres",
            password="postgres",
            connect_timeout=5
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if proxy_metrics table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'proxy_metrics'
            )
        """)
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            logger.error("proxy_metrics table doesn't exist. Run db_fix.py first.")
            return False
        
        # Check if columns already exist to avoid errors
        cursor.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'proxy_metrics'
        """)
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        columns_to_add = {
            'proxy_type': 'TEXT',
            'proxy_group': 'TEXT',
            'location': 'TEXT'
        }
        
        # Add each missing column if it doesn't exist
        for column, data_type in columns_to_add.items():
            if column not in existing_columns:
                logger.info(f"Adding missing column '{column}' to proxy_metrics table")
                cursor.execute(f"ALTER TABLE proxy_metrics ADD COLUMN {column} {data_type}")
                
                # Update with default values based on proxy_id
                # Using a basic strategy to fill with reasonable defaults
                if column == 'proxy_type':
                    cursor.execute("UPDATE proxy_metrics SET proxy_type = 'http' WHERE proxy_type IS NULL")
                elif column == 'proxy_group':
                    cursor.execute("UPDATE proxy_metrics SET proxy_group = 'default' WHERE proxy_group IS NULL")
                elif column == 'location':
                    cursor.execute("UPDATE proxy_metrics SET location = 'unknown' WHERE location IS NULL")
                    
                logger.info(f"Column '{column}' added successfully")
            else:
                logger.info(f"Column '{column}' already exists in proxy_metrics table")
        
        # Create index on proxy_id for faster queries
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_proxy_metrics_proxy_id ON proxy_metrics(proxy_id)")
            logger.info("Created index on proxy_metrics(proxy_id)")
        except Exception as e:
            logger.warning(f"Could not create index: {e}")
        
        # Close connection
        cursor.close()
        conn.close()
        
        logger.info("Successfully fixed proxy_metrics table schema")
        return True
    except Exception as e:
        logger.error(f"Error fixing proxy_metrics table: {e}")
        return False

def update_monitoring_proxy_metrics_creation():
    """
    Update the monitoring.py file to include the new columns 
    in future table creations
    """
    try:
        # Read the monitoring.py file
        monitoring_file = '/home/<USER>/appsUndCode/current projects/trend-crawler/monitoring.py'
        with open(monitoring_file, 'r') as f:
            content = f.read()
        
        # Find the proxy_metrics table creation and replace it
        old_table_creation = '''CREATE TABLE IF NOT EXISTS proxy_metrics (
                    id SERIAL PRIMARY KEY,
                    proxy_id TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN NOT NULL,
                    response_time REAL,
                    status_code INTEGER,
                    error_message TEXT
                )'''
        
        new_table_creation = '''CREATE TABLE IF NOT EXISTS proxy_metrics (
                    id SERIAL PRIMARY KEY,
                    proxy_id TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN NOT NULL,
                    response_time REAL,
                    status_code INTEGER,
                    error_message TEXT,
                    proxy_type TEXT DEFAULT 'http',
                    proxy_group TEXT DEFAULT 'default',
                    location TEXT DEFAULT 'unknown'
                )'''
        
        # Only replace if the old pattern is found
        if old_table_creation in content:
            updated_content = content.replace(old_table_creation, new_table_creation)
            
            # Create a backup of the original file
            backup_file = f"{monitoring_file}.bak"
            logger.info(f"Creating backup of monitoring.py at {backup_file}")
            with open(backup_file, 'w') as f:
                f.write(content)
                
            # Write the updated content
            with open(monitoring_file, 'w') as f:
                f.write(updated_content)
                
            logger.info("Updated monitoring.py with new proxy_metrics table schema")
            return True
        else:
            logger.warning("Could not find the proxy_metrics table creation in monitoring.py")
            return False
    except Exception as e:
        logger.error(f"Error updating monitoring.py: {e}")
        return False

if __name__ == "__main__":
    success = fix_proxy_metrics_table()
    if success:
        logger.info("Successfully fixed proxy_metrics table schema")
    else:
        logger.error("Failed to fix proxy_metrics table schema")
        
    # Also update the monitoring.py file for future table creations
    if update_monitoring_proxy_metrics_creation():
        logger.info("Successfully updated monitoring.py")
    else:
        logger.warning("Could not update monitoring.py file")
