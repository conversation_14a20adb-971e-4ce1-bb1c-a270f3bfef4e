/* Main styles for Trend Crawler Dashboard */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --info-color: #3498db;
    --success-color: #2ecc71;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f6f9;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

header {
    background-color: var(--dark-color);
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
}

.logo {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 1rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem;
}

nav a:hover {
    color: var(--primary-color);
}

main {
    padding: 2rem 0;
}

h1 {
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.dashboard-card {
    background-color: white;
    border-radius: 5px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.dashboard-card h2 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--dark-color);
}

.chart-container {
    height: 250px;
    margin-bottom: 1rem;
}

.card-footer {
    display: flex;
    justify-content: flex-end;
}

.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 3px;
    text-decoration: none;
}

.btn:hover {
    background-color: #2980b9;
}

.alerts-list {
    max-height: 250px;
    overflow-y: auto;
}

.alert {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-radius: 3px;
    border-left: 3px solid;
}

.alert:last-child {
    margin-bottom: 0;
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    border-color: var(--warning-color);
}

.alert-error {
    background-color: rgba(231, 76, 60, 0.1);
    border-color: var(--error-color);
}

.alert-info {
    background-color: rgba(52, 152, 219, 0.1);
    border-color: var(--info-color);
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.1);
    border-color: var(--success-color);
}

.timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: block;
    margin-bottom: 0.25rem;
}

footer {
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
    color: #7f8c8d;
    font-size: 0.875rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f4f6f9;
    font-weight: bold;
}

tr:hover {
    background-color: #f4f6f9;
}

/* Media queries */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    nav {
        flex-direction: column;
    }

    nav ul {
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    nav ul li {
        margin: 0.5rem;
    }
}

/* Authentication styles */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
}

.auth-card {
    background-color: white;
    border-radius: 5px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.auth-form {
    margin-bottom: 1rem;
}

.auth-footer {
    text-align: center;
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: 1rem;
}

.auth-message {
    margin-top: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 3px;
    cursor: pointer;
    width: 100%;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.1);
    border-color: var(--success-color);
    padding: 0.75rem;
    border-radius: 3px;
    border-left: 3px solid;
}

.alert-error {
    background-color: rgba(231, 76, 60, 0.1);
    border-color: var(--error-color);
    padding: 0.75rem;
    border-radius: 3px;
    border-left: 3px solid;
}
