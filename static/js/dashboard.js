/* Main JavaScript for Trend Crawler Dashboard */

// Function to format dates
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat('en-US').format(num);
}

// Function to format percentage
function formatPercent(num) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(num / 100);
}

// Function to handle API errors
function handleApiError(error, elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `<div class="alert alert-error">
            <p>Error loading data: ${error.message}</p>
        </div>`;
    }
    console.error('API Error:', error);
}

// Function to refresh dashboard data
function refreshDashboard() {
    const refreshButtons = document.querySelectorAll('.refresh-btn');
    refreshButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.innerHTML = '<p>Loading data...</p>';
                // Load data based on target ID
                // Implementation depends on the specific dashboard section
            }
        });
    });
}

// Initialize event handlers when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize refresh buttons
    refreshDashboard();

    // Set up interval for auto-refresh if data-refresh attribute exists
    document.querySelectorAll('[data-refresh]').forEach(element => {
        const refreshInterval = parseInt(element.getAttribute('data-refresh')) * 1000;
        if (!isNaN(refreshInterval) && refreshInterval > 0) {
            setInterval(() => {
                // Trigger refresh for this element
                const event = new Event('refresh');
                element.dispatchEvent(event);
            }, refreshInterval);
        }
    });
});