#!/bin/bash

# This script activates the trend-crawler environment and runs the scraper

# Determine the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Change to the script directory
cd "$SCRIPT_DIR"

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "Error: conda is not available. Please install Anaconda or Miniconda."
    exit 1
fi

# Activate the trend-crawler environment
source "$(conda info --base)/etc/profile.d/conda.sh"
if conda activate trend-crawler; then
    echo "Activated trend-crawler environment"
else
    echo "Error: trend-crawler environment not found."
    echo "Please run ./setup.sh first to set up the environment."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Error: .env file not found."
    echo "Please run ./setup.sh first to create the .env file."
    exit 1
fi

# Run the scraper based on the argument passed
if [ "$1" == "twitter" ]; then
    echo "Running Twitter scraper..."
    python twitter_x_scraper.py
elif [ "$1" == "dashboard" ]; then
    echo "Starting web dashboard..."
    python web_dashboard.py
elif [ "$1" == "trends" ]; then
    echo "Running trend crawler..."
    python trend_crawler.py
else
    echo "Usage: ./run_scraper.sh [twitter|dashboard|trends]"
    echo ""
    echo "Available commands:"
    echo "  twitter    - Run the Twitter/X scraper"
    echo "  dashboard  - Start the web dashboard"
    echo "  trends     - Run the trend crawler"
fi
