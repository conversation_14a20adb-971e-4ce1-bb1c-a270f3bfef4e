#!/usr/bin/env python3
"""
Web Dashboard Module for Trend-Crawler

This module provides a web interface with dashboards and reports for the
trend-crawler system, including:
- Scraper performance metrics
- CAPTCHA detection statistics
- Proxy performance monitoring
- Alerts and notifications
- System health overview
- User authentication and management
"""

import os
import datetime
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
import secrets
from datetime import timedelta

# Web framework
from fastapi import (
    FastAPI, APIRouter, HTTPException, Depends, Request, Form, status
)
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import CryptContext
from passlib.exc import UnknownHashError
from pydantic import BaseModel, Field

# Import monitoring system
try:
    from monitoring import monitor
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    logging.warning(
        "Monitoring module not available. Some features will be limited."
    )

# Import CAPTCHA solver
try:
    from captcha_solver import captcha_solver
    CAPTCHA_SOLVER_AVAILABLE = True
except ImportError:
    CAPTCHA_SOLVER_AVAILABLE = False
    logging.warning(
        "CAPTCHA solver not available. Some features will be limited."
    )

# Try importing PostgreSQL
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor, DictCursor
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
    logging.warning("PostgreSQL support not available. Some features will be limited.")

# Add this to the imports section at the top of the file (around line 33)
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError as e:
    PSUTIL_AVAILABLE = False
    logging.error(
        "Failed to import 'psutil'. System monitoring features will be disabled. "
        "This usually means the package is not installed in your Python environment. "
        "Please ensure 'psutil' is listed in your 'requirements.txt' and install dependencies via "
        "'pip install -r requirements.txt' in your active 'trend-crawler' environment. "
        f"Original error: {e}"
    )

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_fallback_detection_stats():
    """Return fallback detection stats when CAPTCHA solver is not available."""
    return {
        "recaptcha_detected": 0,
        "hcaptcha_detected": 0,
        "cloudflare_detected": 0,
        "other_detected": 0,
        "total_detected": 0
    }


# Current year for templates (avoids using Django-specific {% now %} tag)
def get_current_year():
    """Return the current year as a string."""
    return str(datetime.datetime.now().year)

# JWT Settings
SECRET_KEY = os.environ.get("SECRET_KEY", secrets.token_hex(32))
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 8  # 8 hours

# Security settings
DEFAULT_ADMIN_EMAIL = os.environ.get("ADMIN_EMAIL", "<EMAIL>")
DEFAULT_ADMIN_FULLNAME = os.environ.get("ADMIN_FULLNAME", "Admin User")
DEFAULT_ADMIN_USERNAME = os.environ.get("ADMIN_USERNAME", "admin")
ENFORCE_PASSWORD_CHANGE = True  # Enforce password change on first login

# Store whether any password changes have been applied since startup
# This helps us track if first-login password change has occurred
ADMIN_FIRST_LOGIN = True

def generate_secure_password():
    """Generate a secure random password or use the one from environment.

    This ensures that when deployed in production, we can set a secure password
    via environment variables instead of hardcoding it.
    """
    # Check if password is provided via environment variable
    env_password = os.environ.get("ADMIN_PASSWORD")
    if (env_password):
        return env_password

    # Use 'admin' as the default password
    password = "admin"

    # Log that we're using the default password
    logger.warning("Using default admin password 'admin'. User will be forced to change on first login.")
    return password

# Password hasher
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token")
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="api/v1/auth/token", auto_error=False)

# User models
class User(BaseModel):
    """Base user model."""
    username: str
    email: str
    full_name: Optional[str] = None
    disabled: Optional[bool] = False
    is_admin: Optional[bool] = False

class UserInDB(User):
    """User model with password hash."""
    password_hash: str

class Token(BaseModel):
    """Token model for JWT authentication."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Token data model."""
    username: str

class UserCreate(BaseModel):
    """User creation model."""
    username: str
    email: str
    password: str
    full_name: Optional[str] = None
    is_admin: Optional[bool] = False

class PasswordChange(BaseModel):
    """Password change model."""
    current_password: str
    new_password: str

# Proxy model for adding new proxies
class ProxyCreate(BaseModel):
    """Request model for creating a new proxy."""
    ip: str
    port: int
    type: str  # "http", "https", "socks4", "socks5"
    group: str  # "us", "eu", "asia", "residential", "datacenter"
    username: Optional[str] = None
    password: Optional[str] = None
    location: Optional[str] = None

# CAPTCHA Feedback model
class CaptchaFeedbackRequest(BaseModel):
    """Request model for CAPTCHA feedback."""
    captcha_id: str
    correct_solution: Optional[str] = None
    feedback_type: str
    comments: Optional[str] = None

# Integration Test model
class IntegrationTestRequest(BaseModel):
    """Request model for integration tests."""
    target_type: str  # "scraper", "proxy", "captcha_solver"
    target_name: str
    test_type: str  # "connectivity", "performance", "accuracy"
    params: Optional[Dict[str, Any]] = None

# Fallback in-memory database for when PostgreSQL is not available
USERS_DB = {}

# User database connection
def get_db_connection():
    """Get a database connection."""
    if not POSTGRES_AVAILABLE:
        raise Exception("PostgreSQL support not available")

    try:
        conn = psycopg2.connect(
            host=os.environ.get("DB_HOST", "localhost"),
            port=os.environ.get("DB_PORT", "5432"),
            dbname=os.environ.get("DB_NAME", "scraper_metrics"),
            user=os.environ.get("DB_USER", "postgres"),
            password=os.environ.get("DB_PASSWORD", "postgres")
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise

# Ensure users table exists
def init_user_db():
    """Initialize the user database."""
    global USERS_DB, POSTGRES_AVAILABLE
    USERS_DB = {}  # Initialize empty fallback dictionary

    if not POSTGRES_AVAILABLE:
        logger.warning("PostgreSQL not available, using in-memory user database")
        # Add a default admin user to in-memory DB with admin/admin credentials
        admin_password = "admin"
        USERS_DB[DEFAULT_ADMIN_USERNAME] = {
            "username": DEFAULT_ADMIN_USERNAME,
            "email": DEFAULT_ADMIN_EMAIL,
            "full_name": DEFAULT_ADMIN_FULLNAME,
            "disabled": False,
            "is_admin": True,
            "password_hash": pwd_context.hash(admin_password),
            "needs_password_change": ENFORCE_PASSWORD_CHANGE  # Flag to enforce password change on first login
        }
        logger.info(f"Created default admin user with username: {DEFAULT_ADMIN_USERNAME}, password: {admin_password}")
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Create users table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            username VARCHAR(50) PRIMARY KEY,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100),
            disabled BOOLEAN DEFAULT FALSE,
            is_admin BOOLEAN DEFAULT FALSE,
            password_hash VARCHAR(200) NOT NULL,
            needs_password_change BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")

        # Check if admin user exists. If so, ensure password is correct. If not, create admin user.
        cursor.execute("SELECT username FROM users WHERE username = %s", (DEFAULT_ADMIN_USERNAME,))
        admin_exists = cursor.fetchone()

        admin_password = "admin"  # Default password for admin
        hashed_admin_password = pwd_context.hash(admin_password)

        if not admin_exists:
            # Create admin user
            cursor.execute('''
            INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ''', (
                DEFAULT_ADMIN_USERNAME,
                DEFAULT_ADMIN_EMAIL,
                DEFAULT_ADMIN_FULLNAME,
                False,
                True,
                hashed_admin_password,
                ENFORCE_PASSWORD_CHANGE
            ))
            logger.info(f"Created default admin user '{DEFAULT_ADMIN_USERNAME}' with password '{admin_password}'.")
        else:
            # Admin user exists, update password_hash and needs_password_change flag
            cursor.execute('''
            UPDATE users
            SET password_hash = %s, needs_password_change = %s, updated_at = CURRENT_TIMESTAMP
            WHERE username = %s
            ''', (
                hashed_admin_password,
                ENFORCE_PASSWORD_CHANGE,
                DEFAULT_ADMIN_USERNAME
            ))
            logger.info(f"Admin user '{DEFAULT_ADMIN_USERNAME}' exists. Ensured password is '{admin_password}' and 'needs_password_change' is up to date.")

        conn.commit()

        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")

        # Check if admin user exists, create if not
        cursor.execute("SELECT * FROM users WHERE username = %s", ("admin",))
        if cursor.fetchone() is None:
            # Create admin user with admin/admin credentials
            admin_password = "admin"
            cursor.execute('''
            INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ''', (
                DEFAULT_ADMIN_USERNAME,
                DEFAULT_ADMIN_EMAIL,
                DEFAULT_ADMIN_FULLNAME,
                False,
                True,
                pwd_context.hash(admin_password),  # Use admin as the password
                ENFORCE_PASSWORD_CHANGE
            ))
            logger.info(f"Created default admin user with username: {DEFAULT_ADMIN_USERNAME}, password: {admin_password}")

        conn.commit()
        cursor.close()
        conn.close()
        logger.info("User database initialized")
    except Exception as e:
        logger.error(f"Error initializing user database: {e}")
        # Fallback to in-memory database if DB initialization fails
        logger.warning("Using in-memory user database as fallback")
        # Use admin/admin as default credentials
        admin_password = "admin"
        USERS_DB[DEFAULT_ADMIN_USERNAME] = {
            "username": DEFAULT_ADMIN_USERNAME,
            "email": DEFAULT_ADMIN_EMAIL,
            "full_name": DEFAULT_ADMIN_FULLNAME,
            "disabled": False,
            "is_admin": True,
            "password_hash": pwd_context.hash(admin_password),
            "needs_password_change": ENFORCE_PASSWORD_CHANGE  # Flag to enforce password change on first login
        }
        logger.info(f"Created default admin user with username: {DEFAULT_ADMIN_USERNAME}, password: {admin_password}")
        POSTGRES_AVAILABLE = False

# Password functions
def verify_password(plain_password, hashed_password):
    """Verify a password against a hash."""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except UnknownHashError:
        logger.warning(f"Unable to verify password for hash: {hashed_password[:6]}...: unknown hash format")
        return False

def get_password_hash(password):
    """Get password hash."""
    return pwd_context.hash(password)

# User functions
def get_user(username: str):
    """Get a user by username."""
    if not POSTGRES_AVAILABLE:
        # Fall back to in-memory database
        user_data = USERS_DB.get(username)
        if user_data:
            return UserInDB(**user_data)
        return None

    try:
        conn = psycopg2.connect(
            host=os.environ.get("DB_HOST", "localhost"),
            port=os.environ.get("DB_PORT", "5432"),
            dbname=os.environ.get("DB_NAME", "scraper_metrics"),
            user=os.environ.get("DB_USER", "postgres"),
            password=os.environ.get("DB_PASSWORD", "postgres")
        )

        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cursor.execute("SELECT * FROM users WHERE username = %s", (username,))

        user_record = cursor.fetchone()
        cursor.close()
        conn.close()

        if user_record:
            user_dict = dict(user_record)
            return UserInDB(**user_dict)
        return None
    except Exception as e:
        logger.error(f"Error getting user from database: {e}")
        # Fall back to in-memory database on database error
        user_data = USERS_DB.get(username)
        if user_data:
            return UserInDB(**user_data)
        return None

def authenticate_user(username: str, password: str):
    """Authenticate a user."""
    user = get_user(username)
    if not user:
        return False
    if not verify_password(password, user.password_hash):
        return False
    return user

# Token functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.datetime.utcnow() + expires_delta
    else:
        expire = datetime.datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# Authentication dependency
async def get_token_from_header_or_cookie(
    request: Request,
    token_from_header: Optional[str] = Depends(oauth2_scheme_optional) # Try header first
) -> Optional[str]:
    if token_from_header:
        return token_from_header

    token_from_cookie = request.cookies.get("access_token_cookie")
    return token_from_cookie

async def get_current_user(request: Request, token: Optional[str] = Depends(get_token_from_header_or_cookie)):
    """Get the current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"}, # Or potentially redirect to login via cookie mechanism
    )
    if token is None:
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception

        # Store needs_password_change in request state
        needs_password_change = payload.get("needs_password_change", False) # Default to False if not in token
        request.state.needs_password_change = needs_password_change

        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(request: Request, current_user: User = Depends(get_current_user)):
    """Get the current active user."""
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")

    needs_password_change = getattr(request.state, "needs_password_change", False)

    # Define allowed paths when password change is needed
    allowed_paths_when_password_change_needed = [
        "/account",
        "/api/v1/users/change-password",
        "/login",
        "/api/v1/auth/token",
        "/api/v1/auth/me",
        "/logout" # Added logout path
    ]

    if needs_password_change and request.url.path not in allowed_paths_when_password_change_needed:
        # Also check if the request is for a static file, which should be allowed
        if not request.url.path.startswith("/static"):
            # Also check if the request is for the root path, which might redirect to login/dashboard
            if request.url.path == "/":
                # Allow root path, it will handle its own redirection logic
                pass
            else:
                return RedirectResponse(url="/account?prompt_change=true", status_code=status.HTTP_307_TEMPORARY_REDIRECT)

    return current_user

async def get_admin_user(request: Request, current_user: User = Depends(get_current_active_user)):
    """Get current user and verify they are an admin."""
    # Pass the request object to get_current_active_user if it's part of the dependency chain
    # However, Depends() handles this automatically if get_current_active_user is updated.
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized for admin actions"
        )
    return current_user

# Create FastAPI app
app = FastAPI(
    title="Trend Crawler Dashboard",
    description="Web dashboard for the trend-crawler system",
    version="1.0.0"
)

# Create static directory if it doesn't exist
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
css_dir = static_dir / "css"
css_dir.mkdir(exist_ok=True)
js_dir = static_dir / "js"
js_dir.mkdir(exist_ok=True)

# Create templates directory if it doesn't exist
templates_dir = Path("templates")
templates_dir.mkdir(exist_ok=True)

# Create templates
templates = Jinja2Templates(directory="templates")

# Add get_current_year function to template context
templates.env.globals["get_current_year"] = get_current_year

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create router for API endpoints
api_router = APIRouter(prefix="/api/v1")

# Create router for authentication endpoints
auth_router = APIRouter(prefix="/auth")

# Create router for user management endpoints
user_router = APIRouter(prefix="/users")

# Create router for CAPTCHA endpoints
captcha_router = APIRouter(prefix="/captchas")

# Create router for integration test endpoints
integration_router = APIRouter(prefix="/integration")

# Create router for dashboard pages
dashboard_router = APIRouter()

# Original models
class ScraperStatsRequest(BaseModel):
    """Request model for scraper statistics."""
    scraper_name: str
    hours: int = Field(24, description="Number of hours to look back")

class ProxyStatsRequest(BaseModel):
    """Request model for proxy statistics."""
    proxy_id: Optional[str] = Field(None, description="Proxy ID (optional)")
    hours: int = Field(24, description="Number of hours to look back")

class AlertRequest(BaseModel):
    """Request model for sending alerts."""
    alert_type: str
    severity: str = Field(..., description="Alert severity (info, warning, error, critical)")
    message: str
    details: Optional[Dict[str, Any]] = Field(None, description="Additional alert details")

class ThresholdRequest(BaseModel):
    """Request model for setting alert thresholds."""
    metric: str
    threshold: float
    channel: Optional[str] = Field(None, description="Alert channel to use (optional)")

# Authentication endpoints
@auth_router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """Login and get access token."""
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if this user needs to change their password
    needs_password_change = False
    if POSTGRES_AVAILABLE:
        # Check the PostgreSQL database for the needs_password_change flag
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE username = %s", (user.username,))
            user_record = cursor.fetchone()
            cursor.close()
            conn.close()

            # Check if the column exists (in case of older DB schema)
            if user_record and 'needs_password_change' in user_record:
                needs_password_change = user_record['needs_password_change']
        except Exception as e:
            logger.error(f"Error checking password change status: {e}")
            # Fall back to in-memory check
            needs_password_change = USERS_DB.get(user.username, {}).get("needs_password_change", False)
    else:
        # Use in-memory database
        needs_password_change = USERS_DB.get(user.username, {}).get("needs_password_change", False)

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "needs_password_change": needs_password_change},
        expires_delta=access_token_expires
    )

    # If first login, include that in the response so frontend can redirect to change password
    response_content = {
        "access_token": access_token,
        "token_type": "bearer",
        "needs_password_change": needs_password_change
    }
    response = JSONResponse(content=response_content)
    response.set_cookie(
        key="access_token_cookie",
        value=access_token,
        httponly=True,
        path="/",
        samesite="lax",
        secure=False # Should be True in production if using HTTPS
    )
    return response

@auth_router.get("/me", response_model=User)
async def read_users_me(request: Request, current_user: User = Depends(get_current_active_user)):
    """Get current user information."""
    return current_user

# User management endpoints (admin only)
@user_router.post("/", response_model=User)
async def create_user(user: UserCreate, request: Request, current_user: User = Depends(get_admin_user)):
    """Create a new user."""
    if not POSTGRES_AVAILABLE:
        # Fallback to in-memory database
        if user.username in USERS_DB: # type: ignore
            raise HTTPException(status_code=400, detail="Username already registered")

        user_dict = user.model_dump()
        hashed_password = get_password_hash(user_dict.pop("password"))

        new_user_data = { # Renamed to avoid conflict with the 'user' parameter
            **user_dict,
            "disabled": False,
            "password_hash": hashed_password
        }

        USERS_DB[user.username] = new_user_data # type: ignore
        return User(**new_user_data) # type: ignore

    # Use PostgreSQL database
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=DictCursor)

        # Check if username already exists
        cursor.execute("SELECT * FROM users WHERE username = %s", (user.username,))
        if cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=400, detail="Username already registered")

        # Check if email already exists
        cursor.execute("SELECT * FROM users WHERE email = %s", (user.email,))
        if cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=400, detail="Email already registered")

        # Create new user
        user_dict = user.model_dump()
        hashed_password = get_password_hash(user_dict.pop("password"))

        cursor.execute("""
        INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING *
        """, (
            user.username,
            user.email,
            user.full_name,
            False,
            user.is_admin,
            hashed_password,
            ENFORCE_PASSWORD_CHANGE  # Force new users to change password on first login
        ))

        new_user = dict(cursor.fetchone())
        conn.commit()
        cursor.close()
        conn.close()

        return User(**new_user)
    except Exception as e:
        logger.error(f"Error creating user in database: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@user_router.get("/", response_model=List[User])
async def list_users(request: Request, current_user: User = Depends(get_admin_user)):
    """List all users."""
    if not POSTGRES_AVAILABLE:
        # Fallback to in-memory database
        return [User(**u) for u in USERS_DB.values()] # Renamed loop variable

    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=DictCursor)

        # Get all users
        cursor.execute("SELECT * FROM users")
        users = cursor.fetchall()

        cursor.close()
        conn.close()

        return [User(**dict(user)) for user in users]
    except Exception as e:
        logger.error(f"Error listing users from database: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@user_router.delete("/{username}")
async def delete_user(username: str, request: Request, current_user: User = Depends(get_admin_user)):
    """Delete a user."""
    if username == current_user.username:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")

    if not POSTGRES_AVAILABLE:
        # Fallback to in-memory database
        if username not in USERS_DB: # type: ignore
            raise HTTPException(status_code=404, detail="User not found")

        del USERS_DB[username] # type: ignore
        return {"status": "success", "message": f"User {username} deleted"}

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if user exists
        cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
        if cursor.fetchone() is None:
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail="User not found")

        # Delete user
        cursor.execute("DELETE FROM users WHERE username = %s", (username,))
        conn.commit()
        cursor.close()
        conn.close()

        return {"status": "success", "message": f"User {username} deleted"}
    except Exception as e:
        logger.error(f"Error deleting user from database: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@user_router.post("/change-password")
async def change_password(
    request: Request, # Added request
    password_change: PasswordChange,
    current_user: User = Depends(get_current_active_user)
):
    """Change the password for the current user."""
    # The current_user already has the request object implicitly via Depends(get_current_active_user)
    # which itself depends on get_current_user(request: Request, ...)
    if POSTGRES_AVAILABLE:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Verify current password
            cursor.execute("SELECT password_hash FROM users WHERE username = %s",
                          (current_user.username,))
            user_record = cursor.fetchone()
            if not user_record:
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail="User not found")

            password_hash = user_record[0] # type: ignore
            if not verify_password(password_change.current_password, password_hash): # type: ignore
                cursor.close()
                conn.close()
                raise HTTPException(status_code=400, detail="Current password is incorrect")

            # Update password and reset the needs_password_change flag
            cursor.execute("""
                UPDATE users
                SET password_hash = %s, needs_password_change = false, updated_at = CURRENT_TIMESTAMP
                WHERE username = %s
                """,
                (get_password_hash(password_change.new_password), current_user.username)
            )

            conn.commit()
            cursor.close()
            conn.close()

            # Update request state as password has been changed
            if hasattr(request.state, "needs_password_change"):
                # Generate new token
                access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
                new_access_token = create_access_token(
                    data={"sub": current_user.username, "needs_password_change": False},
                    expires_delta=access_token_expires
                )
                response_content_pg = {
                    "status": "success",
                    "message": "Password updated successfully",
                    "access_token": new_access_token,
                    "token_type": "bearer"
                }
                response_pg = JSONResponse(content=response_content_pg)
                response_pg.set_cookie(
                    key="access_token_cookie",
                    value=new_access_token,
                    httponly=True,
                    path="/",
                    samesite="lax",
                    secure=False # Should be True in production
                )
                return response_pg
        except Exception as e:
            logger.error(f"Error updating password in database: {e}")
            # Fall through to in-memory DB if database update fails

    # Fallback to in-memory user database
    user_data_fallback = USERS_DB.get(current_user.username) # Renamed variable
    if not user_data_fallback: # Renamed variable
        raise HTTPException(status_code=404, detail="User not found")

    # Verify current password
    if not verify_password(password_change.current_password, user_data_fallback["password_hash"]): # Renamed variable
        raise HTTPException(status_code=400, detail="Current password is incorrect")

    # Hash and set new password
    user_data_fallback["password_hash"] = get_password_hash(password_change.new_password) # Renamed variable

    # Reset the password change flag
    user_data_fallback["needs_password_change"] = False # Renamed variable

    # Update request state as password has been changed
    if hasattr(request.state, "needs_password_change"):
        request.state.needs_password_change = False

    # Generate new token for in-memory DB case
    access_token_expires_fallback = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    new_access_token_fallback = create_access_token(
        data={"sub": current_user.username, "needs_password_change": False},
        expires_delta=access_token_expires_fallback
    )
    response_content_fallback = {
        "status": "success",
        "message": "Password updated successfully",
        "access_token": new_access_token_fallback,
        "token_type": "bearer"
    }
    response_fallback = JSONResponse(content=response_content_fallback)
    response_fallback.set_cookie(
        key="access_token_cookie",
        value=new_access_token_fallback,
        httponly=True,
        path="/",
        samesite="lax",
        secure=False # Should be True in production
    )
    return response_fallback

@user_router.post("/{username}/reset-password", response_model=dict)
async def reset_password(
    username: str,
    new_password: str = Form(...),
    current_user: User = Depends(get_admin_user)
):
    """Reset a user's password (admin only)."""
    if POSTGRES_AVAILABLE:
        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Check if user exists
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            if cursor.fetchone() is None:
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail="User not found")

            # Set new password and enforce password change on next login
            cursor.execute("""
                UPDATE users
                SET password_hash = %s, needs_password_change = true, updated_at = CURRENT_TIMESTAMP
                WHERE username = %s
                """,
                (get_password_hash(new_password), username)
            )

            conn.commit()
            cursor.close()
            conn.close()

            return {"status": "success", "message": f"Password reset for user {username}"}
        except Exception as e:
            logger.error(f"Error resetting password in database: {e}")
            # Fall through to in-memory DB if database update fails

    # Fallback to in-memory user database
    if username not in USERS_DB: # type: ignore
        raise HTTPException(status_code=404, detail="User not found")

    # Hash and set new password
    USERS_DB[username]["password_hash"] = get_password_hash(new_password) # type: ignore
    # Set flag to force password change on next login
    USERS_DB[username]["needs_password_change"] = True # type: ignore

    return {"status": "success", "message": f"Password reset for user {username}"}

# CAPTCHA endpoints
@captcha_router.get("/stats")
async def get_captcha_stats(request: Request, hours: int = 24): # Added request
    """Get CAPTCHA solver statistics."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth if needed
    try:
        if CAPTCHA_SOLVER_AVAILABLE:
            # Get real stats from CAPTCHA solver
            stats = captcha_solver.get_metrics() # type: ignore
            return stats
        else:
            # If CAPTCHA solver not available, try database
            if POSTGRES_AVAILABLE:
                try:
                    conn = get_db_connection()
                    cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

                    # Get stats from captcha_metrics table
                    cursor.execute("""
                        SELECT
                            COUNT(*) FILTER (WHERE success = true) as solved_count,
                            COUNT(*) FILTER (WHERE success = false) as failed_count,
                            AVG(solve_time) as avg_solve_time,
                            MIN(solve_time) as min_solve_time,
                            MAX(solve_time) as max_solve_time
                        FROM captcha_metrics
                        WHERE timestamp >= NOW() - INTERVAL '%s hours'
                    """, (hours,))

                    stats_data = cursor.fetchone() # Renamed variable
                    cursor.close()
                    conn.close()

                    if stats_data: # Renamed variable
                        return stats_data # Renamed variable
                    else:
                        # Return empty stats if no data
                        return {
                            "solved_count": 0,
                            "failed_count": 0,
                            "avg_solve_time": 0,
                            "min_solve_time": 0,
                            "max_solve_time": 0
                        }
                except Exception as db_error:
                    logger.error(f"Database fallback for CAPTCHA stats failed: {db_error}")
                    # Return empty stats as fallback
                    return {
                        "solved_count": 0,
                        "failed_count": 0,
                        "avg_solve_time": 0,
                        "min_solve_time": 0,
                        "max_solve_time": 0
                    }
            else:
                # If neither CAPTCHA solver nor database is available, return empty stats
                logger.warning("Neither CAPTCHA solver nor database available. Returning empty stats.")
                return {
                    "solved_count": 0,
                    "failed_count": 0,
                    "avg_solve_time": 0,
                    "min_solve_time": 0,
                    "max_solve_time": 0
                }
    except Exception as e:
        logger.error(f"Error getting CAPTCHA stats: {e}")
        # Return empty stats instead of failing completely
        return {
            "solved_count": 0,
            "failed_count": 0,
            "avg_solve_time": 0,
            "min_solve_time": 0,
            "max_solve_time": 0
        }

@captcha_router.get("/detection-stats")
async def get_detection_stats(request: Request): # Added request
    """Get CAPTCHA detection heuristics statistics."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    if not CAPTCHA_SOLVER_AVAILABLE:
        # Return fallback data instead of error
        logger.warning("CAPTCHA solver not available, returning fallback detection stats")
        return get_fallback_detection_stats() # type: ignore

    try:
        stats_data = captcha_solver.get_detection_stats() # type: ignore # Renamed variable
        # If no stats returned, use fallback data
        if not stats_data: # Renamed variable
            return get_fallback_detection_stats() # type: ignore
        return stats_data # Renamed variable
    except Exception as e:
        logger.error(f"Error getting detection stats: {e}")
        # Return fallback data instead of error
        return get_fallback_detection_stats() # type: ignore

@captcha_router.post("/feedback")
async def submit_captcha_feedback(
    request: Request, # Added request
    feedback: CaptchaFeedbackRequest,
    current_user: User = Depends(get_current_active_user)
):
    """Submit feedback for a CAPTCHA solution."""
    # current_user is already resolved with request by Depends
    if not CAPTCHA_SOLVER_AVAILABLE:
        raise HTTPException(
            status_code=501, # type: ignore
            detail="CAPTCHA solver not available. Some features may be limited."
        )

    try:
        success = captcha_solver.submit_feedback( # type: ignore
            captcha_id=feedback.captcha_id,
            correct_solution=feedback.correct_solution,
            feedback_type=feedback.feedback_type,
            comments=feedback.comments
        )

        if success:
            return {"status": "success", "message": "Feedback submitted successfully"}
        else:
            raise HTTPException(
                status_code=404, # type: ignore
                detail="CAPTCHA result not found in history"
            )
    except Exception as e:
        logger.error(f"Error submitting CAPTCHA feedback: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error submitting CAPTCHA feedback: {str(e)}"
        )

@captcha_router.get("/feedback-history")
async def get_feedback_history(request: Request, hours: int = 24, type: Optional[str] = None): # Added request, Optional for type
    """Get the CAPTCHA feedback history."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        if CAPTCHA_SOLVER_AVAILABLE:
            # Get real feedback history from CAPTCHA solver
            history_data = captcha_solver.get_feedback_history() # type: ignore # Renamed variable

            # Filter by CAPTCHA type if specified
            if type and type != "all":
                history_data = [item for item in history_data if item.get('type') == type] # Renamed variable

            # Sort by timestamp (most recent first)
            history_data = sorted(history_data, key=lambda x: x.get('timestamp', ''), reverse=True) # Renamed variable

            return {"solutions": history_data} # Renamed variable
        else:
            # Try to get data from database if CAPTCHA solver not available
            if POSTGRES_AVAILABLE:
                try:
                    conn = get_db_connection()
                    cursor = conn.cursor(cursor_factory=RealDictCursor)

                    time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

                    # Build query based on filter
                    db_query = """
                        SELECT
                            id as captcha_id,
                            timestamp,
                            captcha_type as type,
                            target_url as url,
                            solver_used as solver,
                            success,
                            solve_time
                        FROM captcha_solutions
                        WHERE timestamp > %s
                    """ # Renamed variable
                    params_list = [time_limit] # Renamed variable

                    if type and type != "all":
                        db_query += " AND captcha_type = %s" # Renamed variable
                        params_list.append(type) # Renamed variable

                    db_query += " ORDER BY timestamp DESC" # Renamed variable

                    cursor.execute(db_query, params_list) # Renamed variables
                    solutions_data = cursor.fetchall() # Renamed variable
                    cursor.close()
                    conn.close()

                    # Format timestamps for display
                    for sol_item in solutions_data: # Renamed variables
                        if sol_item['timestamp']: # Renamed variable
                            sol_item['timestamp'] = sol_item['timestamp'].strftime('%Y-%m-%d %H:%M:%S') # Renamed variable

                    return {"solutions": solutions_data} # Renamed variable
                except Exception as db_error:
                    logger.error(f"Database fallback for CAPTCHA feedback history failed: {db_error}")
                    return {"solutions": []}

            # Return empty response if neither CAPTCHA solver nor database is available
            logger.warning("Neither CAPTCHA solver nor database available for feedback history")
            return {"solutions": []}
    except Exception as e:
        logger.error(f"Error getting CAPTCHA feedback history: {e}")
        return {"solutions": []}

# Integration test endpoints
@integration_router.post("/run")
async def run_integration_test(
    integration_request: IntegrationTestRequest, # Renamed to avoid conflict with request: Request
    request: Request, # Added request
    current_user: User = Depends(get_admin_user)
):
    """Run an integration test between system components."""
    # current_user is already resolved with request by Depends
    if not MONITORING_AVAILABLE:
        raise HTTPException(
            status_code=501, # type: ignore
            detail="Monitoring not available. Integration tests cannot be run."
        )

    try:
        # Use the monitoring system to run integration tests
        if integration_request.target_type == "scraper": # Renamed variable
            result_data = monitor.test_scraper_integration( # type: ignore # Renamed variable
                scraper_name=integration_request.target_name, # Renamed variable
                test_type=integration_request.test_type, # Renamed variable
                params=integration_request.params # Renamed variable
            )
        elif integration_request.target_type == "proxy": # Renamed variable
            result_data = monitor.test_proxy_integration( # type: ignore # Renamed variable
                proxy_id=integration_request.target_name, # Renamed variable
                test_type=integration_request.test_type, # Renamed variable
                params=integration_request.params # Renamed variable
            )
        elif integration_request.target_type == "captcha_solver": # Renamed variable
            if not CAPTCHA_SOLVER_AVAILABLE:
                raise HTTPException(
                    status_code=501, # type: ignore
                    detail="CAPTCHA solver not available. Cannot test CAPTCHA integration."
                )
            result_data = monitor.test_captcha_integration( # type: ignore # Renamed variable
                solver_name=integration_request.target_name, # Renamed variable
                test_type=integration_request.test_type, # Renamed variable
                params=integration_request.params # Renamed variable
            )
        else:
            raise HTTPException(
                status_code=400, # type: ignore
                detail=f"Unknown target type: {integration_request.target_type}" # Renamed variable
            )

        return {
            "status": "success",
            "test_id": result_data.get("test_id"), # Renamed variable
            "result": result_data # Renamed variable
        }
    except Exception as e:
        logger.error(f"Error running integration test: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error running integration test: {str(e)}"
        )

@integration_router.get("/history")
async def get_integration_test_history(request: Request, current_user: User = Depends(get_admin_user)): # Added request
    """Get integration test history (admin only)."""
    # current_user is already resolved with request by Depends
    if not MONITORING_AVAILABLE:
        raise HTTPException(
            status_code=501, # type: ignore
            detail="Monitoring not available. Some features may be limited."
        )

    try:
        history_data = monitor.get_integration_test_history() # type: ignore # Renamed variable
        return history_data # Renamed variable
    except Exception as e:
        logger.error(f"Error getting integration test history: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error getting integration test history: {str(e)}"
        )

# Original API Endpoints
@api_router.get("/stats/scrapers")
async def get_scraper_stats(request: Request, scraper_name: str, hours: int = 24): # Added request
    """Get statistics for a scraper."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    if not MONITORING_AVAILABLE:
        raise HTTPException(
            status_code=501, # type: ignore
            detail="Monitoring not available. Some features may be limited."
        )

    try:
        stats_data = monitor.get_scraper_stats(scraper_name, hours) # type: ignore # Renamed variable
        return stats_data # Renamed variable
    except Exception as e:
        logger.error(f"Error getting scraper stats: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error getting scraper stats: {str(e)}"
        )

@api_router.get("/stats/proxies")
async def get_proxy_stats(request: Request, proxy_id: Optional[str] = None, hours: int = 24): # Added request
    """Get statistics for a proxy or all proxies."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            stats_data = monitor.get_proxy_stats(proxy_id, hours) # type: ignore # Renamed variable
            if stats_data: # Renamed variable
                return stats_data # Renamed variable

        # If monitoring is not available or returned no data, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

                if proxy_id:
                    # Query for specific proxy
                    cursor.execute("""
                        SELECT
                            proxy_id,
                            COUNT(*) as total_requests,
                            SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
                            SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
                            AVG(response_time) as avg_response_time,
                            MIN(response_time) as min_response_time,
                            MAX(response_time) as max_response_time,
                            MAX(timestamp) as last_used,
                            proxy_type as type,
                            proxy_group as group,
                            location
                        FROM proxy_metrics
                        WHERE proxy_id = %s AND timestamp > %s
                        GROUP BY proxy_id, proxy_type, proxy_group, location
                    """, (proxy_id, time_limit))
                else:
                    # Query for all proxies
                    cursor.execute("""
                        SELECT
                            proxy_id,
                            COUNT(*) as total_requests,
                            SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
                            SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
                            AVG(response_time) as avg_response_time,
                            MIN(response_time) as min_response_time,
                            MAX(response_time) as max_response_time,
                            MAX(timestamp) as last_used,
                            proxy_type as type,
                            proxy_group as group,
                            location
                        FROM proxy_metrics
                        WHERE timestamp > %s
                        GROUP BY proxy_id, proxy_type, proxy_group, location
                    """, (time_limit,))

                result_data = cursor.fetchall() # Renamed variable
                cursor.close()
                conn.close()

                # Format response
                formatted_stats = {} # Renamed variable
                for row_data in result_data: # Renamed variables
                    p_id = row_data['proxy_id'] # Renamed variable

                    # Calculate success rate
                    s_rate = 0 # Renamed variable
                    if row_data['total_requests'] and row_data['total_requests'] > 0:
                        s_rate = (row_data['successful_requests'] / row_data['total_requests']) * 100 # Renamed variable

                    formatted_stats[p_id] = { # Renamed variables
                        'total_requests': row_data['total_requests'],
                        'successful_requests': row_data['successful_requests'],
                        'failed_requests': row_data['failed_requests'],
                        'success_rate': s_rate, # Renamed variable
                        'avg_response_time': row_data['avg_response_time'],
                        'min_response_time': row_data['min_response_time'],
                        'max_response_time': row_data['max_response_time'],
                        'last_used': row_data['last_used'].isoformat() if row_data['last_used'] else None,
                        'type': row_data['type'],
                        'group': row_data['group'],
                        'location': row_data['location']
                    }

                return formatted_stats # Renamed variable

            except Exception as db_error:
                logger.error(f"Database fallback for proxy stats failed: {db_error}")
                # Return empty dict as a last resort
                return {}

        # If neither monitoring nor database available, return empty response
        logger.warning("Neither monitoring nor database available for proxy stats")
        return {}

    except Exception as e:
        logger.error(f"Error getting proxy stats: {e}")
        # Return empty dict instead of failing completely
        return {}

@api_router.post("/proxies")
async def add_proxy(
    request: Request, # Added request
    proxy: ProxyCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Add a new proxy to the system."""
    # current_user is already resolved with request by Depends
    try:
        # Try to add proxy using monitoring system first
        if MONITORING_AVAILABLE:
            result_data = monitor.add_proxy({ # type: ignore # Renamed variable
                "ip": proxy.ip,
                "port": proxy.port,
                "type": proxy.type,
                "group": proxy.group,
                "username": proxy.username,
                "password": proxy.password,
                "location": proxy.location
            })
            if result_data: # Renamed variable
                return {"status": "success", "message": "Proxy added successfully"}

        # If monitoring is not available, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor()

                # Insert new proxy
                cursor.execute("""
                INSERT INTO proxies (
                    ip_address, port, proxy_type, proxy_group,
                    username, password, location, status, created_at
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
                """, (
                    proxy.ip,
                    proxy.port,
                    proxy.type,
                    proxy.group,
                    proxy.username,
                    proxy.password,
                    proxy.location,
                    'active',  # Default status
                    datetime.datetime.now()
                ))

                p_id = cursor.fetchone()[0] # Renamed variable
                conn.commit()
                cursor.close()
                conn.close()

                return {
                    "status": "success",
                    "message": "Proxy added successfully",
                    "proxy_id": p_id # Renamed variable
                }

            except Exception as db_error:
                logger.error(f"Database error adding proxy: {db_error}")
                raise HTTPException(
                    status_code=500, # type: ignore
                    detail=f"Database error: {str(db_error)}"
                )

        # If neither monitoring nor database is available
        logger.warning("Cannot add proxy - both monitoring and database unavailable")
        raise HTTPException(
            status_code=503, # type: ignore
            detail="Service unavailable - cannot add proxy at this time"
        )

    except Exception as e:
        logger.error(f"Error adding proxy: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error adding proxy: {str(e)}"
        )

@api_router.post("/alerts")
async def send_alert(alert_request: AlertRequest, request: Request): # Renamed, Added request
    """Send an alert."""
    # current_user: User = Depends(get_current_active_user) # Add if auth needed
    if not MONITORING_AVAILABLE:
        raise HTTPException(
            status_code=501, # type: ignore
            detail="Monitoring not available. Some features may be limited."
        )

    try:
        monitor.send_alert( # type: ignore
            alert_request.alert_type, # Renamed variable
            alert_request.severity, # Renamed variable
            alert_request.message, # Renamed variable
            alert_request.details # Renamed variable
        )
        return {"status": "alert sent"}
    except Exception as e:
        logger.error(f"Error sending alert: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error sending alert: {str(e)}"
        )

@api_router.post("/thresholds")
async def set_threshold(threshold_request: ThresholdRequest, request: Request): # Renamed, Added request
    """Set a threshold for alerting."""
    # current_user: User = Depends(get_current_active_user) # Add if auth needed
    if not MONITORING_AVAILABLE:
        raise HTTPException(
            status_code=501, # type: ignore
            detail="Monitoring not available. Some features may be limited."
        )

    try:
        monitor.set_alert_threshold( # type: ignore
            threshold_request.metric, # Renamed variable
            threshold_request.threshold, # Renamed variable
            threshold_request.channel # Renamed variable
        )
        return {"status": "threshold set"}
    except Exception as e:
        logger.error(f"Error setting threshold: {e}")
        raise HTTPException(
            status_code=500, # type: ignore
            detail=f"Error setting threshold: {str(e)}"
        )

@api_router.get("/alerts/recent")
async def get_recent_alerts(request: Request, limit: int = 3): # Added request
    """Get the most recent alerts."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        if (MONITORING_AVAILABLE):
            # Fetch real alert data from the monitoring system
            return monitor.get_recent_alerts(limit=limit) # type: ignore
        else:
            # If monitoring is not available, try to fetch from database
            if POSTGRES_AVAILABLE:
                try:
                    conn = get_db_connection()
                    cursor = conn.cursor(cursor_factory=RealDictCursor)

                    cursor.execute("""
                        SELECT
                            alert_id,
                            alert_type,
                            severity,
                            message,
                            created_at as timestamp,
                            details
                        FROM alerts
                        ORDER BY created_at DESC
                        LIMIT %s
                    """, (limit,))

                    alerts_data = cursor.fetchall() # Renamed variable
                    cursor.close()
                    conn.close()

                    return alerts_data # Renamed variable
                except Exception as db_error:
                    logger.error(f"Database fallback for alerts failed: {db_error}")
                    # Return empty list as a last resort
                    return []
            else:
                # Return empty list if neither monitoring nor database is available
                logger.warning("Both monitoring and database unavailable. Returning empty alerts list.")
                return []
    except Exception as e:
        logger.error(f"Error getting recent alerts: {e}")
        # Return an empty list instead of failing entirely
        return []

# Add this endpoint after the existing API router endpoints (around line 954)

@api_router.get("/system/info")
async def get_system_info(request: Request): # Added request
    """Get system information including CPU, memory, disk usage, etc."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        # Try to get data from monitoring system first
        if (MONITORING_AVAILABLE):
            sys_info_data = monitor.get_system_info() # type: ignore # Renamed variable
            if sys_info_data: # Renamed variable
                return sys_info_data # Renamed variable

        # If monitoring is not available or returned no data, try to collect it directly
        import psutil # type: ignore
        import platform
        from datetime import datetime

        # Get CPU info
        cpu_val = psutil.cpu_percent(interval=1) # Renamed variable

        # Get memory info
        mem_info = psutil.virtual_memory() # Renamed variable
        mem_used = round(mem_info.used / (1024 * 1024)) # Renamed variable
        mem_total = round(mem_info.total / (1024 * 1024)) # Renamed variable
        mem_percent = mem_info.percent # Renamed variable

        # Get disk info
        disk_info = psutil.disk_usage('/') # Renamed variable
        disk_val_used = round(disk_info.used / (1024 * 1024 * 1024)) # Renamed variable
        disk_val_total = round(disk_info.total / (1024 * 1024 * 1024)) # Renamed variable
        disk_val_percent = disk_info.percent # Renamed variable

        # Get system uptime
        boot_timestamp = datetime.fromtimestamp(psutil.boot_time()) # Renamed variable
        current_uptime = datetime.now() - boot_timestamp # Renamed variable
        uptime_display_str = f"up {current_uptime.days} days {current_uptime.seconds // 3600}:{(current_uptime.seconds // 60) % 60:02d}" # Renamed variable

        # Get load average
        load_average_tuple = psutil.getloadavg() # Renamed variable

        # Format system info
        formatted_sys_info = { # Renamed variable
            "hostname": platform.node(),
            "cpu_usage": f"~{cpu_val}%", # Renamed variable
            "memory_usage": f"{mem_used}/{mem_total}MB (~{mem_percent}%)", # Renamed variables
            "disk_usage": f"{disk_val_used}/{disk_val_total}GB (~{disk_val_percent}%)", # Renamed variables
            "os_info": f"{platform.system()} {platform.release()} {platform.version()}",
            "kernel": platform.platform(),
            "cpu_info": platform.processor(),
            "python_version": platform.python_version(),
            "uptime": uptime_display_str, # Renamed variable
            "load_avg": f"{load_average_tuple[0]:.2f}, {load_average_tuple[1]:.2f}, {load_average_tuple[2]:.2f}" # Renamed variable
        }

        return formatted_sys_info # Renamed variable
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        # Return fallback data with fields to avoid frontend errors
        return { # type: ignore
            "hostname": "N/A",
            "cpu_usage": "N/A",
            "memory_usage": "N/A",
            "disk_usage": "N/A",
            "os_info": "N/A",
            "kernel": "N/A",
            "cpu_info": "N/A",
            "python_version": "N/A",
            "uptime": "N/A",
            "load_avg": "N/A"
        }

@api_router.get("/system/resources")
async def get_system_resources(request: Request, hours: int = 1): # Added request
    """Get historical system resource usage (CPU, memory, disk)."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            resources_data = monitor.get_system_resources(hours) # type: ignore # Renamed variable
            if resources_data: # Renamed variable
                return resources_data # Renamed variable

        # If monitoring is not available or returned no data, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

                cursor.execute("""
                    SELECT
                        timestamp,
                        cpu_usage,
                        memory_usage,
                        disk_usage
                    FROM system_metrics
                    WHERE timestamp > %s
                    ORDER BY timestamp ASC
                """, (time_limit,))

                result_set = cursor.fetchall() # Renamed variable
                cursor.close()
                conn.close()

                # Format the timestamps to ISO format
                for row_item in result_set: # Renamed variables
                    if row_item['timestamp']: # Renamed variable
                        row_item['timestamp'] = row_item['timestamp'].isoformat() # Renamed variable

                return { # type: ignore
                    "labels": [r['timestamp'] for r in result_set], # Renamed variable
                    "cpu_usage": [r['cpu_usage'] for r in result_set], # Renamed variable
                    "memory_usage": [r['memory_usage'] for r in result_set], # Renamed variable
                    "disk_usage": [r['disk_usage'] for r in result_set] # Renamed variable
                }

            except Exception as db_error:
                logger.error(f"Database fallback for system resources failed: {db_error}")
                # Return empty data as a last resort
                return { # type: ignore
                    "labels": [],
                    "cpu_usage": [],
                    "memory_usage": [],
                    "disk_usage": []
                }

        # If neither monitoring nor database available, return empty response
        logger.warning("Neither monitoring nor database available for system resources")
        return {
            "labels": [],
            "cpu_usage": [],
            "memory_usage": [],
            "disk_usage": []
        }

    except Exception as e:
        logger.error(f"Error getting system resources: {e}")
        # Return empty data instead of failing completely
        return {
            "labels": [],
            "cpu_usage": [],
            "memory_usage": [],
            "disk_usage": []
        }

@api_router.get("/system/api-health")
async def get_api_health(request: Request): # Added request
    """Get API service health stats."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            health_data = monitor.get_api_health() # type: ignore # Renamed variable
            if health_data: # Renamed variable
                return health_data # Renamed variable

        # If monitoring is not available or returned no data, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                # Get latest API health metrics
                cursor.execute("""
                    SELECT
                        api_name,
                        AVG(response_time) as avg_response_time,
                        COUNT(*) FILTER (WHERE success = TRUE) as success_count,
                        COUNT(*) FILTER (WHERE success = FALSE) as error_count
                    FROM api_metrics
                    WHERE timestamp > NOW() - INTERVAL '24 hours'
                    GROUP BY api_name
                """)

                result_set = cursor.fetchall() # Renamed variable
                cursor.close()
                conn.close()

                # Format response
                names_list = [] # Renamed variable
                times_list = [] # Renamed variable
                rates_list = [] # Renamed variable

                for row_item in result_set: # Renamed variables
                    names_list.append(row_item['api_name']) # Renamed variable
                    times_list.append(row_item['avg_response_time']) # Renamed variable

                    # Calculate success rate
                    total_req = row_item['success_count'] + row_item['error_count'] # Renamed variable
                    s_rate = 0 # Renamed variable
                    if total_req > 0: # Renamed variable
                        s_rate = (row_item['success_count'] / total_req) * 100 # Renamed variables
                    rates_list.append(s_rate) # Renamed variable

                return { # type: ignore
                    "api_names": names_list, # Renamed variable
                    "response_times": times_list, # Renamed variable
                    "success_rates": rates_list # Renamed variable
                }

            except Exception as db_error:
                logger.error(f"Database fallback for API health failed: {db_error}")
                # Return empty data as a last resort
                return { # type: ignore
                    "api_names": [],
                    "response_times": [],
                    "success_rates": []
                }

        # If neither monitoring nor database available, return empty response
        logger.warning("Neither monitoring nor database available for API health")
        return { # type: ignore
            "api_names": [],
            "response_times": [],
            "success_rates": []
        }

    except Exception as e:
        logger.error(f"Error getting API health: {e}")
        # Return empty data instead of failing completely
        return { # type: ignore
            "api_names": [],
            "response_times": [],
            "success_rates": []
        }

@api_router.get("/system/db-performance")
async def get_db_performance(request: Request, hours: int = 1): # Added request
    """Get database performance metrics."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            perf_data = monitor.get_db_performance(hours) # type: ignore # Renamed variable
            if perf_data: # Renamed variable
                return perf_data # Renamed variable

        # If monitoring is not available or returned no data, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                time_limit = datetime.datetime.now() - datetime.timedelta(hours=hours)

                cursor.execute("""
                    SELECT
                        timestamp,
                        query_time,
                        active_connections
                    FROM db_performance_metrics
                    WHERE timestamp > %s
                    ORDER BY timestamp ASC
                """, (time_limit,))

                result_set = cursor.fetchall() # Renamed variable
                cursor.close()
                conn.close()

                # Format the timestamps to ISO format
                ts_list = [] # Renamed variable
                qt_list = [] # Renamed variable
                ac_list = [] # Renamed variable

                for row_item in result_set: # Renamed variables
                    ts_list.append(row_item['timestamp'].isoformat() if row_item['timestamp'] else None) # Renamed variable
                    qt_list.append(row_item['query_time']) # Renamed variable
                    ac_list.append(row_item['active_connections']) # Renamed variable

                return { # type: ignore
                    "timestamps": ts_list, # Renamed variable
                    "query_times": qt_list, # Renamed variable
                    "active_connections": ac_list # Renamed variable
                }

            except Exception as db_error:
                logger.error(f"Database fallback for DB performance failed: {db_error}")
                # Return empty data as a last resort
                return { # type: ignore
                    "timestamps": [],
                    "query_times": [],
                    "active_connections": []
                }

        # If neither monitoring nor database available, return empty response
        logger.warning("Neither monitoring nor database available for DB performance")
        return { # type: ignore
            "timestamps": [],
            "query_times": [],
            "active_connections": []
        }

    except Exception as e:
        logger.error(f"Error getting DB performance: {e}")
        # Return empty data instead of failing completely
        return { # type: ignore
            "timestamps": [],
            "query_times": [],
            "active_connections": []
        }

@api_router.get("/system/events")
async def get_system_events(request: Request, limit: int = 20): # Added request
    """Get recent system events."""
    # current_user: User = Depends(get_current_active_user) # Added to ensure auth
    try:
        # Try to get data from monitoring system first
        if MONITORING_AVAILABLE:
            events_data = monitor.get_system_events(limit) # type: ignore # Renamed variable
            if events_data: # Renamed variable
                # Format monitoring data to match frontend expectations
                formatted_events = []
                for event in events_data:
                    # Check if it's already in the correct format (time, type, message)
                    if 'time' in event and 'type' in event and 'message' in event:
                        formatted_events.append(event)
                    else:
                        # Convert from monitoring format to frontend format
                        timestamp_str = event.get('timestamp', '')
                        if timestamp_str:
                            try:
                                # Parse timestamp and format for display
                                if isinstance(timestamp_str, str):
                                    ts_val = datetime.datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                                else:
                                    ts_val = timestamp_str

                                current_time = datetime.datetime.now()

                                # Format timestamp for display
                                if ts_val.date() == current_time.date():
                                    display_time_str = f"Today {ts_val.strftime('%H:%M')}"
                                elif ts_val.date() == (current_time - datetime.timedelta(days=1)).date():
                                    display_time_str = f"Yesterday {ts_val.strftime('%H:%M')}"
                                else:
                                    display_time_str = ts_val.strftime('%Y-%m-%d %H:%M')
                            except:
                                display_time_str = timestamp_str
                        else:
                            display_time_str = 'N/A'

                        formatted_events.append({
                            "time": display_time_str,
                            "type": event.get('event_type', 'Unknown'),
                            "message": event.get('message', 'No message available')
                        })

                return formatted_events

        # If monitoring is not available or returned no data, try database
        if POSTGRES_AVAILABLE:
            try:
                conn = get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)

                # Get recent system events - use correct column names
                cursor.execute("""
                    SELECT
                        timestamp,
                        event_type,
                        message
                    FROM system_events
                    ORDER BY timestamp DESC
                    LIMIT %s
                """, (limit,))

                result_set = cursor.fetchall() # Renamed variable
                cursor.close()
                conn.close()

                # Format events for display
                formatted_events = [] # Renamed variable
                for row_item in result_set: # Renamed variables
                    ts_val = row_item['timestamp'] # Renamed variable
                    current_time = datetime.datetime.now() # Renamed variable

                    # Format timestamp for display
                    if ts_val.date() == current_time.date(): # Renamed variables
                        display_time_str = f"Today {ts_val.strftime('%H:%M')}" # Renamed variables
                    elif ts_val.date() == (current_time - datetime.timedelta(days=1)).date(): # Renamed variables
                        display_time_str = f"Yesterday {ts_val.strftime('%H:%M')}" # Renamed variables
                    else:
                        display_time_str = ts_val.strftime('%Y-%m-%d %H:%M') # Renamed variable

                    formatted_events.append({ # Renamed variable
                        "time": display_time_str, # Renamed variable
                        "type": row_item['event_type'], # Renamed variable
                        "message": row_item['message'] # Renamed variable
                    })

                return formatted_events # Renamed variable

            except Exception as db_error:
                logger.error(f"Database fallback for system events failed: {db_error}")
                # Return empty list as a last resort
                return []

        # If neither monitoring nor database available, return empty response
        logger.warning("Neither monitoring nor database available for system events")
        return []

    except Exception as e:
        logger.error(f"Error getting system events: {e}")
        # Return empty list instead of failing completely
        return []

# Login and register pages
@dashboard_router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Login page."""
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "title": "Login"}
    )

@dashboard_router.get("/register", response_class=HTMLResponse)
async def register_page(request: Request):
    """Register page."""
    return templates.TemplateResponse(
        "register.html",
        {"request": request, "title": "Register"}
    )

# Original Dashboard Pages
@dashboard_router.get("/", response_class=HTMLResponse)
async def root_redirect(request: Request, token: Optional[str] = Depends(get_token_from_header_or_cookie)):
    if not token:
        return RedirectResponse(url="/login")

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: Optional[str] = payload.get("sub")
        if username is None:
            return RedirectResponse(url="/login") # Invalid token payload

        # We need to check if the user exists and is not disabled,
        # similar to get_current_user logic, but without raising HTTPException on failure to find user,
        # as we want to redirect to /login instead.

        user = get_user(username) # Assuming get_user returns None if not found, doesn't raise.

        if user is None or user.disabled:
            return RedirectResponse(url="/login") # User not found or disabled

        needs_password_change = payload.get("needs_password_change", False)

        if needs_password_change:
            # Ensure request.state is available if other parts of the system expect it for this user
            # This is a good place to set it if not already set by a prior dependency.
            if not hasattr(request.state, 'needs_password_change'):
                request.state.needs_password_change = needs_password_change
            if not hasattr(request.state, 'user'):
                request.state.user = user

            return RedirectResponse(url="/account?prompt_change=true", status_code=status.HTTP_307_TEMPORARY_REDIRECT)
        else:
            # Ensure request.state is available for consistency
            if not hasattr(request.state, 'needs_password_change'):
                request.state.needs_password_change = needs_password_change
            if not hasattr(request.state, 'user'):
                request.state.user = user
            return RedirectResponse(url="/dashboard", status_code=status.HTTP_307_TEMPORARY_REDIRECT)

    except JWTError: # Invalid token
        return RedirectResponse(url="/login")

@dashboard_router.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """Main dashboard page."""
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "title": "Trend Crawler Dashboard", "user": current_user}
    )

@dashboard_router.get("/scrapers", response_class=HTMLResponse)
async def get_scrapers_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """Scrapers dashboard page."""
    return templates.TemplateResponse(
        "scrapers.html",
        {"request": request, "title": "Scraper Performance", "user": current_user}
    )

@dashboard_router.get("/proxies", response_class=HTMLResponse)
async def get_proxies_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """Proxies dashboard page."""
    return templates.TemplateResponse(
        "proxies.html",
        {"request": request, "title": "Proxy Performance", "user": current_user}
    )

@dashboard_router.get("/captchas", response_class=HTMLResponse)
async def get_captchas_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """CAPTCHA dashboard page."""
    return templates.TemplateResponse(
        "captchas.html",
        {"request": request, "title": "CAPTCHA Analytics", "user": current_user}
    )

@dashboard_router.get("/alerts", response_class=HTMLResponse)
async def get_alerts_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """Alerts dashboard page."""
    return templates.TemplateResponse(
        "alerts.html",
        {"request": request, "title": "Alerts & Notifications", "user": current_user}
    )

@dashboard_router.get("/system", response_class=HTMLResponse)
async def get_system_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """System health dashboard page."""
    return templates.TemplateResponse(
        "system.html",
        {"request": request, "title": "System Health", "user": current_user}
    )

@dashboard_router.get("/integration-tests", response_class=HTMLResponse)
async def get_integration_tests_dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    """Integration tests dashboard page."""
    return templates.TemplateResponse(
        "integration_tests.html",
        {"request": request, "title": "Integration Tests", "user": current_user}
    )

@dashboard_router.get("/account", response_class=HTMLResponse)
async def get_account_page(request: Request, current_user: User = Depends(get_current_active_user)):
    """Account management page."""
    prompt_change_message = None
    if request.query_params.get("prompt_change") == "true":
        prompt_change_message = "For security reasons, you must change your default password before proceeding."

    return templates.TemplateResponse(
        "account.html",
        {
            "request": request,
            "title": "Account Settings",
            "user": current_user,
            "prompt_change_message": prompt_change_message
        }
    )

@dashboard_router.get("/user-management", response_class=HTMLResponse)
async def get_user_management_page(request: Request, current_user: User = Depends(get_admin_user)):
    """User management page (admin only)."""
    return templates.TemplateResponse(
        "user_management.html",
        {"request": request, "title": "User Management"}
    )

@dashboard_router.get("/logout", response_class=RedirectResponse)
async def logout_and_redirect(request: Request):
    response = RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    response.delete_cookie(key="access_token_cookie", path="/")
    # Client-side should also clear localStorage if it was used.
    return response

# Add routers to app
api_router.include_router(auth_router)
api_router.include_router(user_router)
api_router.include_router(captcha_router)
api_router.include_router(integration_router)
app.include_router(api_router)
app.include_router(dashboard_router)

if __name__ == "__main__":
    import uvicorn

    # Create example HTML templates
    with open("templates/account.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>Account Settings</h2>

    {% if prompt_change_message %}
    <div class="alert alert-warning" role="alert" style="margin-bottom: 1rem;">
        {{ prompt_change_message }}
    </div>
    {% endif %}

    <form id="change-password-form">
        <h3>Change Password</h3>
        <div class="form-group">
            <label for="current_password">Current Password</label>
            <input type="password" id="current_password" name="current_password" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="new_password">New Password</label>
            <input type="password" id="new_password" name="new_password" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="confirm_new_password">Confirm New Password</label>
            <input type="password" id="confirm_new_password" name="confirm_new_password" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">Change Password</button>
        <div id="change-password-message" style="margin-top: 1rem;"></div>
    </form>

    <hr style="margin: 2rem 0;">

    <h3>User Profile</h3>
    <p><strong>Username:</strong> {{ user.username }}</p>
    <p><strong>Email:</strong> {{ user.email }}</p>
    <p><strong>Full Name:</strong> {{ user.full_name if user.full_name else 'Not set' }}</p>

    <!-- Placeholder for other account settings -->

</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const changePasswordForm = document.getElementById('change-password-form');
    const changePasswordMessage = document.getElementById('change-password-message'); // Ensure this div exists in the HTML part

    if (changePasswordForm && changePasswordMessage) {
        changePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmNewPassword = document.getElementById('confirm_new_password').value;

            changePasswordMessage.innerHTML = ''; // Clear previous messages

            if (newPassword !== confirmNewPassword) {
                changePasswordMessage.innerHTML = '<div class="alert alert-error">New passwords do not match.</div>';
                return;
            }
            if (!newPassword) {
                changePasswordMessage.innerHTML = '<div class="alert alert-error">New password cannot be empty.</div>';
                return;
            }
            if (!currentPassword) { // Also check current password
                changePasswordMessage.innerHTML = '<div class="alert alert-error">Current password cannot be empty.</div>';
                return;
            }

            const payload = {
                current_password: currentPassword,
                new_password: newPassword
            };

            try {
                const response = await fetch('/api/v1/users/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
                    },
                    body: JSON.stringify(payload)
                });

                const responseData = await response.json();

                if (response.ok) {
                    changePasswordMessage.innerHTML = `<div class="alert alert-success">${responseData.message || 'Password updated successfully! Redirecting...'}</div>`;
                    if (responseData.access_token) {
                        localStorage.setItem('access_token', responseData.access_token);
                    }
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000); // Redirect after 2 seconds
                } else {
                    changePasswordMessage.innerHTML = `<div class="alert alert-error">${responseData.detail || 'Failed to change password.'}</div>`;
                }
            } catch (error) {
                console.error('Password change error:', error);
                changePasswordMessage.innerHTML = '<div class="alert alert-error">An unexpected error occurred. Please try again.</div>';
            }
        });
    }
});
</script>
{% endblock %}
""")

    with open("templates/base.html", "w") as f:
        f.write("""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Trend Crawler</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@2.0.0"></script>
    {% block head %}{% endblock %}
</head>
<body>
    <div class="container">
        <header>
            <nav>
                <div class="logo">Trend Crawler</div>
                <ul>
                    <li><a href="/">Dashboard</a></li>
                    <li><a href="/scrapers">Scrapers</a></li>
                    <li><a href="/proxies">Proxies</a></li>
                    <li><a href="/captchas">CAPTCHAs</a></li>
                    <li><a href="/alerts">Alerts</a></li>
                    <li><a href="/system">System</a></li>
                    <li><a href="/account">Account</a></li>
                    {% if user and user.is_admin %}
                    <li><a href="/user-management">Users</a></li>
                    {% endif %}
                </ul>
            </nav>
        </header>
        <main>
            <h1>{{ title }}</h1>
            {% block content %}{% endblock %}
        </main>
        <footer>
            <p>Trend Crawler - © {{ get_current_year() }}</p>
        </footer>
    </div>
    <script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>""")

    with open("templates/dashboard.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Scraper Performance</h2>
        <div class="chart-container">
            <canvas id="scraperChart"></canvas>
        </div>
        <div class="card-footer">
            <a href="/scrapers" class="btn">Details</a>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>CAPTCHA Detection</h2>
        <div class="chart-container">
            <canvas id="captchaChart"></canvas>
        </div>
        <div class="card-footer">
            <a href="/captchas" class="btn">Details</a>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Proxy Performance</h2>
        <div class="chart-container">
            <canvas id="proxyChart"></canvas>
        </div>
        <div class="card-footer">
            <a href="/proxies" class="btn">Details</a>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Recent Alerts</h2>
        <div class="alerts-list" id="recentAlerts">
            <p>Loading alerts...</p>
        </div>
        <div class="card-footer">
            <a href="/alerts" class="btn">View All</a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load dashboard data from API
    fetch('/api/v1/stats/scrapers?scraper_name=all&hours=24')
        .then(response => response.json())
        .then(data => {
            // Initialize charts with data
            initScraperChart(data);
        })
        .catch(error => console.error('Error loading scraper data:', error));

    fetch('/api/v1/stats/proxies?hours=24')
        .then(response => response.json())
        .then(data => {
            initProxyChart(data);
        })
        .catch(error => console.error('Error loading proxy data:', error));
});

function initScraperChart(data) {
    const ctx = document.getElementById('scraperChart').getContext('2d');
    const scraperChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(data),
            datasets: [{
                label: 'Success Rate (%)',
                data: Object.values(data).map(value =>
                    value.total_requests > 0 ?
                    (value.successful_requests / value.total_requests) * 100 : 0
                ),
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function initProxyChart(data) {
    const ctx = document.getElementById('proxyChart').getContext('2d');
    const proxyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(data),
            datasets: [{
                label: 'Success Rate (%)',
                data: Object.values(data).map(value => value.success_rate || 0),
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function initCaptchaChart() {
    const ctx = document.getElementById('captchaChart').getContext('2d');
    const captchaChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Solved', 'Failed'],
            datasets: [{
                data: [85, 15],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(255, 99, 132, 0.2)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true
        }
    });
}

// Initialize dummy data for CAPTCHA chart
initCaptchaChart();

// Load recent alerts
const alertElement = document.getElementById('recentAlerts');
alertElement.innerHTML = `
    <div class="alert alert-warning">
        <span class="timestamp">Today 14:23</span>
        <p>High CAPTCHA rate detected for Twitter scraper (15%)</p>
    </div>
    <div class="alert alert-error">
        <span class="timestamp">Yesterday 22:05</span>
        <p>Proxy failure rate above threshold (25%)</p>
    </div>
    <div class="alert alert-info">
        <span class="timestamp">May 7, 09:12</span>
        <p>New scraper registered: CoinMarketCap</p>
    </div>
`;
</script>
{% endblock %}""")

    # Create CSS file
    with open("static/css/style.css", "w") as f:
        f.write("""/* Main styles for Trend Crawler Dashboard */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --info-color: #3498db;
    --success-color: #2ecc71;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f6f9;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

header {
    background-color: var(--dark-color);
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
}

.logo {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 1rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem;
}

nav a:hover {
    color: var(--primary-color);
}

main {
    padding: 2rem 0;
}

h1 {
    margin-bottom: 2rem;
    color: var(--dark-color);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.dashboard-card {
    background-color: white;
    border-radius: 5px;
    padding: 1.5rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.dashboard-card h2 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    color: var(--dark-color);
}

.chart-container {
    height: 250px;
    margin-bottom: 1rem;
}

.card-footer {
    display: flex;
    justify-content: flex-end;
}

.btn {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 3px;
    text-decoration: none;
}

.btn:hover {
    background-color: #2980b9;
}

.alerts-list {
    max-height: 250px;
    overflow-y: auto;
}

.alert {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    border-radius: 3px;
    border-left: 3px solid;
}

.alert:last-child {
    margin-bottom: 0;
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    border-color: var(--warning-color);
}

.alert-error {
    background-color: rgba(231, 76, 60, 0.1);
    border-color: var(--error-color);
}

.alert-info {
    background-color: rgba(52, 152, 219, 0.1);
    border-color: var(--info-color);
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.1);
    border-color: var(--success-color);
}

.timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: block;
    margin-bottom: 0.25rem;
}

footer {
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
    color: #7f8c8d;
    font-size: 0.875rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f4f6f9;
    font-weight: bold;
}

tr:hover {
    background-color: #f4f6f9;
}

/* Media queries */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    nav {
        flex-direction: column;
    }

    nav ul {
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    nav ul li {
        margin: 0.5rem;
    }
}

/* Authentication styles */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
}

.auth-card {
    background-color: white;
    border-radius: 5px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

.auth-form {
    margin-bottom: 1rem;
}

.auth-footer {
    text-align: center;
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: 1rem;
}

.auth-message {
    margin-top: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 3px;
    cursor: pointer;
    width: 100%;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.1);
    border-color: var(--success-color);
    padding: 0.75rem;
    border-radius: 3px;
    border-left: 3px solid;
}

.alert-error {
    background-color: rgba(231, 76, 60, 0.1);
    border-color: var(--error-color);
    padding: 0.75rem;
    border-radius: 3px;
    border-left: 3px solid;
}
""")

    # Create JS file
    with open("static/js/dashboard.js", "w") as f:
        f.write("""/* Main JavaScript for Trend Crawler Dashboard */

// Function to format dates
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat('en-US').format(num);
}

// Function to format percentage
function formatPercent(num) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(num / 100);
}

// Function to handle API errors
function handleApiError(error, elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `<div class="alert alert-error">
            <p>Error loading data: ${error.message}</p>
        </div>`;
    }
    console.error('API Error:', error);
}

// Function to refresh dashboard data
function refreshDashboard() {
    const refreshButtons = document.querySelectorAll('.refresh-btn');
    refreshButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.innerHTML = '<p>Loading data...</p>';
                // Load data based on target ID
                // Implementation depends on the specific dashboard section
            }
        });
    });
}

// Initialize event handlers when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize refresh buttons
    refreshDashboard();

    // Set up interval for auto-refresh if data-refresh attribute exists
    document.querySelectorAll('[data-refresh]').forEach(element => {
        const refreshInterval = parseInt(element.getAttribute('data-refresh')) * 1000;
        if (!isNaN(refreshInterval) && refreshInterval > 0) {
            setInterval(() => {
                // Trigger refresh for this element
                const event = new Event('refresh');
                element.dispatchEvent(event);
            }, refreshInterval);
        }
    });
});""")

    # Create simple scrapers template
    with open("templates/scrapers.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="filters">
    <div class="form-group">
        <label for="scraper-select">Select Scraper:</label>
        <select id="scraper-select" class="form-control">
            <option value="all">All Scrapers</option>
            <option value="twitter_x_scraper">Twitter/X Scraper</option>
            <option value="trend_crawler">Trend Crawler</option>
        </select>
    </div>
    <div class="form-group">
        <label for="time-range">Time Range:</label>
        <select id="time-range" class="form-control">
            <option value="24">Last 24 Hours</option>
            <option value="48">Last 48 Hours</option>
            <option value="168">Last 7 Days</option>
            <option value="720">Last 30 Days</option>
        </select>
    </div>
    <button id="apply-filters" class="btn">Apply Filters</button>
</div>

<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Success Rate</h2>
        <div class="chart-container">
            <canvas id="successRateChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Response Time</h2>
        <div class="chart-container">
            <canvas id="responseTimeChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>CAPTCHA Rates</h2>
        <div class="chart-container">
            <canvas id="captchaRateChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Request Volume</h2>
        <div class="chart-container">
            <canvas id="requestVolumeChart"></canvas>
        </div>
    </div>
</div>

<div class="dashboard-card">
    <h2>Scraper Performance Data</h2>
    <div class="table-container" id="scraperTable">
        <p>Loading scraper data...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const applyFiltersBtn = document.getElementById('apply-filters');
    applyFiltersBtn.addEventListener('click', loadScraperData);

    // Initial data load
    loadScraperData();

    function loadScraperData() {
        const scraperSelect = document.getElementById('scraper-select');
        const timeRange = document.getElementById('time-range');

        const scraper = scraperSelect.value;
        const hours = timeRange.value;

        const url = scraper === 'all'
            ? `/api/v1/stats/scrapers?scraper_name=all&hours=${hours}`
            : `/api/v1/stats/scrapers?scraper_name=${scraper}&hours=${hours}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                updateScraperCharts(data);
                updateScraperTable(data);
            })
            .catch(error => {
                handleApiError(error, 'scraperTable');
            });
    }

    function updateScraperCharts(data) {
        updateSuccessRateChart(data);
        updateResponseTimeChart(data);
        updateCaptchaRateChart(data);
        updateRequestVolumeChart(data);
    }

    function updateSuccessRateChart(data) {
        const ctx = document.getElementById('successRateChart').getContext('2d');

        // Clear existing chart if any
        if (window.successRateChart && typeof window.successRateChart.destroy === 'function') {
            window.successRateChart.destroy();
        }

        // Process data for chart
        let labels, successRates;

        if (Array.isArray(data)) {
            const validData = data.filter(item => item && typeof item === 'object');
            labels = validData.map(item => item.scraper_name);
            successRates = validData.map(item => {
                if (item.total_requests > 0) {
                    return (item.successful_requests / item.total_requests) * 100;
                }
                return 0;
            });
        } else { // Assuming data is an object
            const validKeys = Object.keys(data).filter(key => data[key] && typeof data[key] === 'object');
            labels = validKeys;
            successRates = validKeys.map(key => {
                const item = data[key];
                if (item.total_requests > 0) {
                    return (item.successful_requests / item.total_requests) * 100;
                }
                return 0;
            });
        }

        window.successRateChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Success Rate (%)',
                    data: successRates,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    function updateResponseTimeChart(data) {
        // Similar implementation to success rate chart
        // but focusing on response time metrics
        const ctx = document.getElementById('responseTimeChart').getContext('2d');

        // Generate dummy data for now
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                datasets: [{
                    label: 'Avg Response Time (s)',
                    data: [0.8, 1.2, 0.9, 1.1, 0.7, 1.3, 0.6],
                    borderColor: 'rgba(255, 159, 64, 1)',
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function updateCaptchaRateChart(data) {
        // Implementation for CAPTCHA rate chart
        const ctx = document.getElementById('captchaRateChart').getContext('2d');

        // Generate dummy data for now
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Twitter/X', 'Google', 'TradingView', 'CMC'],
                datasets: [{
                    label: 'CAPTCHA Rate (%)',
                    data: [15, 8, 12, 5],
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }

    function updateRequestVolumeChart(data) {
        // Implementation for request volume chart
        const ctx = document.getElementById('requestVolumeChart').getContext('2d');

        // Generate dummy data for now
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                datasets: [{
                    label: 'Requests',
                    data: [150, 230, 180, 250, 120, 300, 200],
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function updateScraperTable(data) {
        const tableContainer = document.getElementById('scraperTable');

        let tableHTML = `
        <table>
            <thead>
                <tr>
                    <th>Scraper</th>
                    <th>Total Requests</th>
                    <th>Success Rate</th>
                    <th>CAPTCHA Rate</th>
                    <th>Avg Response Time</th>
                </tr>
            </thead>
            <tbody>
        `;

        if (Array.isArray(data)) {
            data.forEach(item => {
                if (item) { // Add null check for item
                    tableHTML += createScraperRow(item);
                }
            });
        } else { // Assuming data is an object
            Object.keys(data).forEach(key => {
                const item = data[key];
                if (item) { // Add null check for item
                    tableHTML += createScraperRow({...item, scraper_name: key});
                }
            });
        }

        tableHTML += `
            </tbody>
        </table>
        `;

        tableContainer.innerHTML = tableHTML;
    }

    function createScraperRow(item) {
        if (!item) {
            console.error("Received null or undefined item in createScraperRow:", item);
            return '';
        }
        const successRate = (item.total_requests && item.total_requests > 0)
            ? ( (item.successful_requests || 0) / item.total_requests) * 100
            : 0;

        const captchaRate = (item.total_requests && item.total_requests > 0)
            ? ( (item.captcha_count || 0) / item.total_requests) * 100
            : 0;

        return `
        <tr>
            <td>${item.scraper_name || 'N/A'}</td>
            <td>${item.total_requests || 0}</td>
            <td>${successRate.toFixed(1)}%</td>
            <td>${captchaRate.toFixed(1)}%</td>
            <td>${item.avg_response_time ? item.avg_response_time.toFixed(2) + 's' : 'N/A'}</td>
        </tr>
        `;
    }
});
</script>
{% endblock %}""")

    # Create login template
    with open("templates/login.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2>Login</h2>
        <form id="login-form" class="auth-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Login</button>
            </div>
            <div class="auth-message" id="login-message"></div>
        </form>
        <div class="auth-footer">
            <p>Don't have an account? <a href="/register">Register</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginMessage = document.getElementById('login-message');

    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // Create form data for API submission
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        fetch('/api/v1/auth/token', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Login failed');
            }
            return response.json();
        })
        .then(data => {
            // Store the token in localStorage
            localStorage.setItem('access_token', data.access_token);

            // Check for needs_password_change flag and redirect accordingly
            if (data.needs_password_change === true) {
                window.location.href = '/account'; // Redirect to account page to change password
            } else {
                window.location.href = '/dashboard'; // Redirect to dashboard
            }
        })
        .catch(error => {
            loginMessage.innerHTML = `<div class="alert alert-error">
                <p>Invalid username or password.</p>
            </div>`;
        });
    });
});
</script>
{% endblock %}""")

    # Create register template
    with open("templates/register.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2>Register</h2>
        <form id="register-form" class="auth-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="full_name">Full Name</label>
                <input type="text" id="full_name" name="full_name" class="form-control">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="confirm_password">Confirm Password</label>
                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Register</button>
            </div>
            <div class="auth-message" id="register-message"></div>
        </form>
        <div class="auth-footer">
            <p>Already have an account? <a href="/login">Login</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('register-form');
    const registerMessage = document.getElementById('register-message');

    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const fullName = document.getElementById('full_name').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        // Check if passwords match
        if (password !== confirmPassword) {
            registerMessage.innerHTML = `<div class="alert alert-error">
                <p>Passwords do not match.</p>
            </div>`;
            return;
        }

        // Create user data for API submission
        const userData = {
            username: username,
            email: email,
            password: password,
            full_name: fullName
        };

        // This registration approach is a security issue and should not be used in production
        // Users should register without admin privileges and admin should approve them
        // or a secure registration token system should be used
        registerMessage.innerHTML = `<div class="alert alert-info">
            <p>Please contact an administrator to create your account.</p>
        </div>`;
        return;

        // This section is disabled due to security concerns
        // Using hardcoded admin credentials is not secure
        /*
        const adminFormData = new FormData();
        adminFormData.append('username', 'admin');
        // Removed hardcoded password
        */

        fetch('/api/v1/auth/token', {
            method: 'POST',
            body: adminFormData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Admin authentication failed');
            }
            return response.json();
        })
        .then(adminData => {
            // Now use the admin token to create the user
            return fetch('/api/v1/users/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${adminData.access_token}`
                },
                body: JSON.stringify(userData)
            });
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Registration failed');
            }
            return response.json();
        })
        .then(data => {
            registerMessage.innerHTML = `<div class="alert alert-success">
                <p>Registration successful! <a href="/login">Login</a> to continue.</p>
            </div>`;
            registerForm.reset();
        })
        .catch(error => {
            registerMessage.innerHTML = `<div class="alert alert-error">
                <p>Registration failed: ${error.message}</p>
            </div>`;
        });
    });
});
</script>
{% endblock %}""")

    # Create system template
    with open("templates/system.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card full-width">
        <h2>System Information</h2>
        <div id="systemInfo"><p>Loading system information...</p></div>
    </div>

    <div class="dashboard-card">
        <h2>System Resource Usage (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="systemResourcesChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>API Service Health (Last 24h)</h2>
        <div class="chart-container">
            <canvas id="apiHealthChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Database Performance (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="dbPerformanceChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card full-width">
        <h2>Recent System Events</h2>
        <div id="systemEvents" class="events-list"><p>Loading system events...</p></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const systemInfoDiv = document.getElementById('systemInfo');
    const systemEventsDiv = document.getElementById('systemEvents');

    // Ensure divs exist before trying to update them
    if (!systemInfoDiv) {
        console.error("System page error: 'systemInfo' div not found.");
        return; // Stop if essential elements are missing
    }
    if (!systemEventsDiv) {
        console.error("System page error: 'systemEvents' div not found.");
        return; // Stop if essential elements are missing
    }

    // Simplified fetchWithAuth for example (actual implementation might be more complex)
    // This assumes the cookie handles auth for GET requests, or token is in localStorage for manual header setting
    async function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('access_token'); // Still attempt to use localStorage for JS-initiated calls
        const headers = {
            'Content-Type': 'application/json', // Good practice to set content type for fetch
            ...options.headers
        };
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        // Cookies will be sent automatically by the browser for same-origin requests.
        return fetch(url, { ...options, headers });
    }

    function formatKey(key) {
        const result = key.replace(/_/g, " ").replace(/([A-Z])/g, " $1"); // Handle snake_case first, then camelCase
        return result.charAt(0).toUpperCase() + result.slice(1).trim();
    }

    function loadSystemInfo() {
        console.log("Attempting to load system info...");
        systemInfoDiv.innerHTML = '<p>Loading system information...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/info')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System info API response:", data);
                if (data && data.hostname && data.hostname !== 'N/A' && data.hostname !== null) {
                    let html = '<dl class="system-info-list">'; // Using a definition list for semantics
                    for (const key in data) {
                        if (data.hasOwnProperty(key)) {
                             html += `<dt>${formatKey(key)}</dt><dd>${data[key]}</dd>`;
                        }
                    }
                    html += '</dl>';
                    systemInfoDiv.innerHTML = html;
                } else {
                    console.log("System info unavailable (psutil likely missing). API returned:", data);
                    systemInfoDiv.innerHTML = '<p class="alert alert-warning">System information is currently unavailable. This usually means the \\'psutil\\' Python package is not installed or not functioning correctly in the server environment.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading system info:', error);
                systemInfoDiv.innerHTML = `<p class="alert alert-danger">Error loading system information: ${error.message}</p>`;
            });
    }

    function loadSystemEvents() {
        console.log("Attempting to load system events...");
        systemEventsDiv.innerHTML = '<p>Loading system events...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/events')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System events API response:", data);

                if (data && Array.isArray(data) && data.length > 0) {
                    let html = '';
                    data.forEach(event => {
                        html += `<div class="event-item alert alert-info">
                                     <span class="timestamp"><strong>Time:</strong> ${event.time || 'N/A'}</span>
                                     <strong class="event-type">Type:</strong> ${event.type || 'N/A'}
                                     <p><strong>Message:</strong> ${event.message || 'N/A'}</p>
                                 </div>`;
                    });
                    systemEventsDiv.innerHTML = html;

                systemEventsDiv.innerHTML = `<p class="alert alert-danger">Error loading system events: ${error.message}</p>`;
            });
    }

    // Load initial data
    loadSystemInfo();
    loadSystemEvents();

    // Placeholder for other charts on system page - ensure they also have robust loading/error states
    // For example, initSystemResourcesChart(), initApiHealthChart(), initDbPerformanceChart()
    // These should be defined or have placeholders to avoid JS errors if called.
    // Example for one chart:
    const systemResourcesChartCanvas = document.getElementById('systemResourcesChart');
    if (systemResourcesChartCanvas) {
        // Dummy/Placeholder logic - actual chart init would fetch data
        try {
            // const ctx = systemResourcesChartCanvas.getContext('2d');
            // new Chart(ctx, { ... }); // Actual chart initialization
            console.log("Placeholder for System Resources Chart initialization.");
        } catch(e) {
            console.error("Error initializing System Resources Chart:", e);
            systemResourcesChartCanvas.parentElement.innerHTML = "<p class='alert alert-warning'>Could not load System Resources chart.</p>";
        }
    }
    // Repeat for apiHealthChart and dbPerformanceChart
    const apiHealthChartCanvas = document.getElementById('apiHealthChart');
    if (apiHealthChartCanvas) {
        console.log("Placeholder for API Health Chart initialization.");
    }
    const dbPerformanceChartCanvas = document.getElementById('dbPerformanceChart');
    if (dbPerformanceChartCanvas) {
        console.log("Placeholder for DB Performance Chart initialization.");
    }

});
</script>
{% endblock %}

{% block head %}
<style>
.full-width {
    grid-column: 1 / -1; /* Span all columns */
}
.system-info-list dt {
    font-weight: bold;
    margin-top: 0.5rem;
}
.system-info-list dd {
    margin-left: 1rem;
    margin-bottom: 0.5rem;
}
.events-list .event-item {
    margin-bottom: 10px;
    padding: 10px;
    border-left-width: 5px;
}
.events-list .timestamp {
    display: block;
    font-size: 0.9em;
    color: #555;
    margin-bottom: 5px;
}
.events-list .event-type {
    font-weight: bold;
}
</style>
{% endblock %}
""")

    with open("templates/integration_tests.html", "w") as f:
        f.write("""{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>Run Integration Test</h2>
    <form id="integration-test-form" class="form">
        <div class="form-group">
            <label for="target-type">Target Type:</label>
            <select id="target-type" class="form-control" required>
                <option value="scraper">Scraper</option>
                <option value="proxy">Proxy</option>
                <option value="captcha_solver">CAPTCHA Solver</option>
            </select>
        </div>
        <div class="form-group">
            <label for="target-name">Target Name:</label>
            <input type="text" id="target-name" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="test-type">Test Type:</label>
            <select id="test-type" class="form-control" required>
                <option value="connectivity">Connectivity</option>
                <option value="performance">Performance</option>
                <option value="accuracy">Accuracy</option>
            </select>
        </div>
        <div class="form-group">
            <label for="params">Parameters (JSON):</label>
            <textarea id="params" class="form-control" rows="4" placeholder='{"key": "value"}'></textarea>
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-primary">Run Test</button>
        </div>
    </form>
    <div id="test-result" class="test-result"></div>
</div>

<div class="dashboard-card">
    <h2>Integration Test History</h2>
    <div class="table-container" id="testHistoryTable">
        <p>Loading test history...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const integrationTestForm = document.getElementById('integration-test-form');
    const testResultDiv = document.getElementById('test-result');

    // Load test history when page loads
    loadTestHistory();

    // Handle form submission
    integrationTestForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const targetType = document.getElementById('target-type').value;
        const targetName = document.getElementById('target-name').value;
        const testType = document.getElementById('test-type').value;
        const paramsText = document.getElementById('params').value;

        // Parse params or set to null if empty
        let params = null;
        if (paramsText.trim()) {
            try {
                params = JSON.parse(paramsText);
            } catch (err) {
                testResultDiv.innerHTML = `<div class="alert alert-error">
                    <p>Invalid JSON parameters: ${err.message}</p>
                </div>`;
                return;
            }
        }

        // Create test request
        const testRequest = {
            target_type: targetType,
            target_name: targetName,
            test_type: testType,
            params: params
        };

        // Show loading message
        testResultDiv.innerHTML = `<div class="loading">
            <p>Running integration test...</p>
        </div>`;

        // Submit test request to API
        fetch('/api/v1/integration/run', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(testRequest)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Display test result
            const result = data.result;
            const status = result.passed ? 'success' : 'error';

            testResultDiv.innerHTML = `
                <div class="alert alert-${status}">
                    <h3>Test Result: ${result.passed ? 'PASSED' : 'FAILED'}</h3>
                    <p><strong>Test ID:</strong> ${data.test_id}</p>
                    <p><strong>Duration:</strong> ${result.duration.toFixed(2)}s</p>
                    <p><strong>Message:</strong> ${result.message}</p>
                    ${result.details ? `<pre>${JSON.stringify(result.details, null, 2)}</pre>` : ''}
                </div>
            `;

            // Reload test history
            loadTestHistory();
        })
        .catch(error => {
            console.error('Error running integration test:', error);
            testResultDiv.innerHTML = `<div class="alert alert-error">
                <p>Error running integration test: ${error.message}</p>
            </div>`;
        });
    });

    function loadTestHistory() {
        const historyTable = document.getElementById('testHistoryTable');

        fetch('/api/v1/integration/history', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                historyTable.innerHTML = '<p>No integration tests have been run yet.</p>';
                return;
            }

            // Create table with test history
            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>Test ID</th>
                            <th>Target Type</th>
                            <th>Target Name</th>
                            <th>Test Type</th>
                            <th>Status</th>
                            <th>Time</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.forEach(test => {
                const timestamp = new Date(test.timestamp).toLocaleString();
                tableHTML += `
                    <tr>
                        <td>${test.test_id}</td>
                        <td>${test.target_type}</td>
                        <td>${test.target_name}</td>
                        <td>${test.test_type}</td>
                        <td>
                            <span class="status-badge status-${test.passed ? 'good' : 'error'}">
                                ${test.passed ? 'PASSED' : 'FAILED'}
                            </span>
                        </td>
                        <td>${timestamp}</td>
                        <td>${test.duration.toFixed(2)}s</td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            historyTable.innerHTML = tableHTML;
        })
        .catch(error => {
            console.error('Error loading test history:', error);
            historyTable.innerHTML = `<div class="alert alert-error">
                <p>Error loading test history: ${error.message}</p>
            </div>`;
        });
    }
});
</script>
{% endblock %}

{% block head %}
<style>
.test-result {
    margin-top: 1.5rem;
}

.loading {
    text-align: center;
    padding: 1rem;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 3px;
}

pre {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 3px;
    overflow: auto;
    max-height: 300px;
    margin-top: 0.75rem;
}
</style>
{% endblock %}""")

    # Run API server
    logger.info("Starting web dashboard. Run with 'python web_dashboard.py'")
    uvicorn.run(app, host="0.0.0.0", port=8080)
